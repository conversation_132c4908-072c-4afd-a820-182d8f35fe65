{"@@locale": "en", "appTitle": "LifeLoop", "@appTitle": {"description": "The title of the application"}, "goodMorning": "Good Morning", "@goodMorning": {"description": "Morning greeting"}, "goodAfternoon": "Good Afternoon", "@goodAfternoon": {"description": "Afternoon greeting"}, "goodEvening": "Good Evening", "@goodEvening": {"description": "Evening greeting"}, "todaysTasks": "Today's Tasks", "@todaysTasks": {"description": "Header for today's tasks section"}, "upcomingBills": "Upcoming Bills", "@upcomingBills": {"description": "Header for upcoming bills section"}, "planMyDay": "Plan my day", "@planMyDay": {"description": "Header for day planning section"}, "tasks": "Tasks", "@tasks": {"description": "Tasks screen title"}, "bills": "Bills & Payments", "@bills": {"description": "Bills screen title"}, "settings": "Settings", "@settings": {"description": "Settings screen title"}, "all": "All", "@all": {"description": "All items filter"}, "today": "Today", "@today": {"description": "Today filter"}, "pending": "Pending", "@pending": {"description": "Pending status"}, "completed": "Completed", "@completed": {"description": "Completed status"}, "overdue": "Overdue", "@overdue": {"description": "Overdue status"}, "paid": "Paid", "@paid": {"description": "Paid status"}, "upcoming": "Upcoming", "@upcoming": {"description": "Upcoming status"}, "addTask": "Add Task", "@addTask": {"description": "Add task button"}, "addBill": "Add Bill", "@addBill": {"description": "Add bill button"}, "editTask": "Edit Task", "@editTask": {"description": "Edit task dialog title"}, "editBill": "Edit Bill", "@editBill": {"description": "Edit bill dialog title"}, "taskTitle": "Task Title", "@taskTitle": {"description": "Task title field label"}, "billTitle": "Bill <PERSON>", "@billTitle": {"description": "Bill title field label"}, "description": "Description", "@description": {"description": "Description field label"}, "amount": "Amount", "@amount": {"description": "Amount field label"}, "dueDate": "Due Date", "@dueDate": {"description": "Due date field label"}, "priority": "Priority", "@priority": {"description": "Priority field label"}, "category": "Category", "@category": {"description": "Category field label"}, "status": "Status", "@status": {"description": "Status field label"}, "low": "Low", "@low": {"description": "Low priority"}, "medium": "Medium", "@medium": {"description": "Medium priority"}, "high": "High", "@high": {"description": "High priority"}, "urgent": "<PERSON><PERSON>", "@urgent": {"description": "Urgent priority"}, "work": "Work", "@work": {"description": "Work category"}, "personal": "Personal", "@personal": {"description": "Personal category"}, "shopping": "Shopping", "@shopping": {"description": "Shopping category"}, "health": "Health", "@health": {"description": "Health category"}, "finance": "Finance", "@finance": {"description": "Finance category"}, "home": "Home", "@home": {"description": "Home category"}, "other": "Other", "@other": {"description": "Other category"}, "electricity": "Electricity", "@electricity": {"description": "Electricity bill type"}, "water": "Water", "@water": {"description": "Water bill type"}, "gas": "Gas", "@gas": {"description": "Gas bill type"}, "internet": "Internet", "@internet": {"description": "Internet bill type"}, "mobile": "Mobile", "@mobile": {"description": "Mobile bill type"}, "dth": "DTH", "@dth": {"description": "DTH bill type"}, "rent": "Rent", "@rent": {"description": "Rent bill type"}, "insurance": "Insurance", "@insurance": {"description": "Insurance bill type"}, "loan": "Loan", "@loan": {"description": "Loan bill type"}, "save": "Save", "@save": {"description": "Save button"}, "cancel": "Cancel", "@cancel": {"description": "Cancel button"}, "delete": "Delete", "@delete": {"description": "Delete button"}, "edit": "Edit", "@edit": {"description": "Edit button"}, "close": "Close", "@close": {"description": "Close button"}, "search": "Search", "@search": {"description": "Search button"}, "filter": "Filter", "@filter": {"description": "Filter button"}, "noTasksToday": "No tasks for today", "@noTasksToday": {"description": "Empty state message for today's tasks"}, "noUpcomingBills": "No upcoming bills", "@noUpcomingBills": {"description": "Empty state message for upcoming bills"}, "addTaskToGetStarted": "Add a task to get started", "@addTaskToGetStarted": {"description": "Empty state helper text for tasks"}, "addBillToGetStarted": "Tap + to add a new bill", "@addBillToGetStarted": {"description": "Empty state helper text for bills"}, "markAsPaid": "<PERSON> as <PERSON><PERSON>", "@markAsPaid": {"description": "Mark bill as paid button"}, "markAsComplete": "Mark as Complete", "@markAsComplete": {"description": "Mark task as complete button"}, "language": "Language", "@language": {"description": "Language setting"}, "selectLanguage": "Select Language", "@selectLanguage": {"description": "Language selection dialog title"}, "statistics": "Statistics", "@statistics": {"description": "Statistics menu item"}, "export": "Export", "@export": {"description": "Export menu item"}, "taskAddedSuccessfully": "Task added successfully!", "@taskAddedSuccessfully": {"description": "Success message for adding task"}, "billAddedSuccessfully": "<PERSON> added successfully!", "@billAddedSuccessfully": {"description": "Success message for adding bill"}, "taskUpdatedSuccessfully": "Task updated successfully!", "@taskUpdatedSuccessfully": {"description": "Success message for updating task"}, "billUpdatedSuccessfully": "Bill updated successfully!", "@billUpdatedSuccessfully": {"description": "Success message for updating bill"}, "deleteTaskConfirmation": "Are you sure you want to delete this task?", "@deleteTaskConfirmation": {"description": "Confirmation message for deleting task"}, "deleteBillConfirmation": "Are you sure you want to delete this bill?", "@deleteBillConfirmation": {"description": "Confirmation message for deleting bill"}, "recurringBill": "Recurring Bill", "@recurringBill": {"description": "Recurring bill option"}, "monthly": "Monthly", "@monthly": {"description": "Monthly recurrence"}, "quarterly": "Quarterly", "@quarterly": {"description": "Quarterly recurrence"}, "halfYearly": "Half Yearly", "@halfYearly": {"description": "Half yearly recurrence"}, "yearly": "Yearly", "@yearly": {"description": "Yearly recurrence"}, "greatAllCaughtUp": "Great! You're all caught up", "@greatAllCaughtUp": {"description": "Day planner message when no urgent tasks"}, "noUrgentTasksOrBills": "No urgent tasks or bills for today", "@noUrgentTasksOrBills": {"description": "Day planner subtitle when no urgent items"}, "heresSuggestion": "Sure, here's a suggestion:", "@heresSuggestion": {"description": "Day planner suggestion intro"}}