// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Telugu (`te`).
class AppLocalizationsTe extends AppLocalizations {
  AppLocalizationsTe([String locale = 'te']) : super(locale);

  @override
  String get appTitle => 'లైఫ్‌లూప్';

  @override
  String get goodMorning => 'శుభోదయం';

  @override
  String get goodAfternoon => 'నమస్కారం';

  @override
  String get goodEvening => 'శుభ సాయంత్రం';

  @override
  String get todaysTasks => 'నేటి పనులు';

  @override
  String get upcomingBills => 'రాబోయే బిల్లులు';

  @override
  String get planMyDay => 'నా రోజును ప్లాన్ చేయండి';

  @override
  String get tasks => 'పనులు';

  @override
  String get bills => 'బిల్లులు మరియు చెల్లింపులు';

  @override
  String get settings => 'సెట్టింగ్‌లు';

  @override
  String get all => 'అన్నీ';

  @override
  String get today => 'నేడు';

  @override
  String get pending => 'పెండింగ్';

  @override
  String get completed => 'పూర్తయింది';

  @override
  String get overdue => 'ఆలస్యం';

  @override
  String get paid => 'చెల్లించబడింది';

  @override
  String get upcoming => 'రాబోయే';

  @override
  String get addTask => 'పని జోడించండి';

  @override
  String get addBill => 'బిల్లు జోడించండి';

  @override
  String get editTask => 'పనిని సవరించండి';

  @override
  String get editBill => 'బిల్లును సవరించండి';

  @override
  String get taskTitle => 'పని శీర్షిక';

  @override
  String get billTitle => 'బిల్లు శీర్షిక';

  @override
  String get description => 'వివరణ';

  @override
  String get amount => 'మొత్తం';

  @override
  String get dueDate => 'చెల్లించాల్సిన తేదీ';

  @override
  String get priority => 'ప్రాధాన్యత';

  @override
  String get category => 'వర్గం';

  @override
  String get status => 'స్థితి';

  @override
  String get low => 'తక్కువ';

  @override
  String get medium => 'మధ్యస్థ';

  @override
  String get high => 'అధిక';

  @override
  String get urgent => 'అత్యవసర';

  @override
  String get work => 'పని';

  @override
  String get personal => 'వ్యక్తిగత';

  @override
  String get shopping => 'షాపింగ్';

  @override
  String get health => 'ఆరోగ్యం';

  @override
  String get finance => 'ఆర్థిక';

  @override
  String get home => 'ఇల్లు';

  @override
  String get other => 'ఇతర';

  @override
  String get electricity => 'విద్యుత్';

  @override
  String get water => 'నీరు';

  @override
  String get gas => 'గ్యాస్';

  @override
  String get internet => 'ఇంటర్నెట్';

  @override
  String get mobile => 'మొబైల్';

  @override
  String get dth => 'డిటిహెచ్';

  @override
  String get rent => 'అద్దె';

  @override
  String get insurance => 'బీమా';

  @override
  String get loan => 'రుణం';

  @override
  String get save => 'సేవ్ చేయండి';

  @override
  String get cancel => 'రద్దు చేయండి';

  @override
  String get delete => 'తొలగించండి';

  @override
  String get edit => 'సవరించండి';

  @override
  String get close => 'మూసివేయండి';

  @override
  String get search => 'వెతకండి';

  @override
  String get filter => 'ఫిల్టర్';

  @override
  String get noTasksToday => 'నేడు పనులు లేవు';

  @override
  String get noUpcomingBills => 'రాబోయే బిల్లులు లేవు';

  @override
  String get addTaskToGetStarted => 'ప్రారంభించడానికి ఒక పనిని జోడించండి';

  @override
  String get addBillToGetStarted => 'కొత్త బిల్లు జోడించడానికి + నొక్కండి';

  @override
  String get markAsPaid => 'చెల్లించినట్లు గుర్తించండి';

  @override
  String get markAsComplete => 'పూర్తయినట్లు గుర్తించండి';

  @override
  String get language => 'భాష';

  @override
  String get selectLanguage => 'భాషను ఎంచుకోండి';

  @override
  String get statistics => 'గణాంకాలు';

  @override
  String get export => 'ఎగుమతి';

  @override
  String get taskAddedSuccessfully => 'పని విజయవంతంగా జోడించబడింది!';

  @override
  String get billAddedSuccessfully => 'బిల్లు విజయవంతంగా జోడించబడింది!';

  @override
  String get taskUpdatedSuccessfully => 'పని విజయవంతంగా అప్‌డేట్ చేయబడింది!';

  @override
  String get billUpdatedSuccessfully => 'బిల్లు విజయవంతంగా అప్‌డేట్ చేయబడింది!';

  @override
  String get deleteTaskConfirmation => 'మీరు ఖచ్చితంగా ఈ పనిని తొలగించాలనుకుంటున్నారా?';

  @override
  String get deleteBillConfirmation => 'మీరు ఖచ్చితంగా ఈ బిల్లును తొలగించాలనుకుంటున్నారా?';

  @override
  String get recurringBill => 'పునరావృత బిల్లు';

  @override
  String get monthly => 'నెలవారీ';

  @override
  String get quarterly => 'త్రైమాసిక';

  @override
  String get halfYearly => 'అర్ధవార్షిక';

  @override
  String get yearly => 'వార్షిక';

  @override
  String get greatAllCaughtUp => 'అద్భుతం! మీరు అన్నింటినీ పూర్తి చేశారు';

  @override
  String get noUrgentTasksOrBills => 'నేడు అత్యవసర పనులు లేదా బిల్లులు లేవు';

  @override
  String get heresSuggestion => 'అవును, ఇదిగో ఒక సూచన:';
}
