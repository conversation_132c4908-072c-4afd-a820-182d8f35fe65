// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Hindi (`hi`).
class AppLocalizationsHi extends AppLocalizations {
  AppLocalizationsHi([String locale = 'hi']) : super(locale);

  @override
  String get appTitle => 'लाइफलूप';

  @override
  String get goodMorning => 'सुप्रभात';

  @override
  String get goodAfternoon => 'नमस्ते';

  @override
  String get goodEvening => 'शुभ संध्या';

  @override
  String get todaysTasks => 'आज के कार्य';

  @override
  String get upcomingBills => 'आगामी बिल';

  @override
  String get planMyDay => 'मेरे दिन की योजना';

  @override
  String get tasks => 'कार्य';

  @override
  String get bills => 'बिल और भुगतान';

  @override
  String get settings => 'सेटिंग्स';

  @override
  String get all => 'सभी';

  @override
  String get today => 'आज';

  @override
  String get pending => 'लंबित';

  @override
  String get completed => 'पूर्ण';

  @override
  String get overdue => 'देर से';

  @override
  String get paid => 'भुगतान किया';

  @override
  String get upcoming => 'आगामी';

  @override
  String get addTask => 'कार्य जोड़ें';

  @override
  String get addBill => 'बिल जोड़ें';

  @override
  String get editTask => 'कार्य संपादित करें';

  @override
  String get editBill => 'बिल संपादित करें';

  @override
  String get taskTitle => 'कार्य शीर्षक';

  @override
  String get billTitle => 'बिल शीर्षक';

  @override
  String get description => 'विवरण';

  @override
  String get amount => 'राशि';

  @override
  String get dueDate => 'देय तिथि';

  @override
  String get priority => 'प्राथमिकता';

  @override
  String get category => 'श्रेणी';

  @override
  String get status => 'स्थिति';

  @override
  String get low => 'कम';

  @override
  String get medium => 'मध्यम';

  @override
  String get high => 'उच्च';

  @override
  String get urgent => 'तत्काल';

  @override
  String get work => 'काम';

  @override
  String get personal => 'व्यक्तिगत';

  @override
  String get shopping => 'खरीदारी';

  @override
  String get health => 'स्वास्थ्य';

  @override
  String get finance => 'वित्त';

  @override
  String get home => 'घर';

  @override
  String get other => 'अन्य';

  @override
  String get electricity => 'बिजली';

  @override
  String get water => 'पानी';

  @override
  String get gas => 'गैस';

  @override
  String get internet => 'इंटरनेट';

  @override
  String get mobile => 'मोबाइल';

  @override
  String get dth => 'डीटीएच';

  @override
  String get rent => 'किराया';

  @override
  String get insurance => 'बीमा';

  @override
  String get loan => 'ऋण';

  @override
  String get save => 'सेव करें';

  @override
  String get cancel => 'रद्द करें';

  @override
  String get delete => 'हटाएं';

  @override
  String get edit => 'संपादित करें';

  @override
  String get close => 'बंद करें';

  @override
  String get search => 'खोजें';

  @override
  String get filter => 'फिल्टर';

  @override
  String get noTasksToday => 'आज कोई कार्य नहीं';

  @override
  String get noUpcomingBills => 'कोई आगामी बिल नहीं';

  @override
  String get addTaskToGetStarted => 'शुरू करने के लिए एक कार्य जोड़ें';

  @override
  String get addBillToGetStarted => 'नया बिल जोड़ने के लिए + दबाएं';

  @override
  String get markAsPaid => 'भुगतान के रूप में चिह्नित करें';

  @override
  String get markAsComplete => 'पूर्ण के रूप में चिह्नित करें';

  @override
  String get language => 'भाषा';

  @override
  String get selectLanguage => 'भाषा चुनें';

  @override
  String get statistics => 'आंकड़े';

  @override
  String get export => 'निर्यात';

  @override
  String get taskAddedSuccessfully => 'कार्य सफलतापूर्वक जोड़ा गया!';

  @override
  String get billAddedSuccessfully => 'बिल सफलतापूर्वक जोड़ा गया!';

  @override
  String get taskUpdatedSuccessfully => 'कार्य सफलतापूर्वक अपडेट किया गया!';

  @override
  String get billUpdatedSuccessfully => 'बिल सफलतापूर्वक अपडेट किया गया!';

  @override
  String get deleteTaskConfirmation => 'क्या आप वाकई इस कार्य को हटाना चाहते हैं?';

  @override
  String get deleteBillConfirmation => 'क्या आप वाकई इस बिल को हटाना चाहते हैं?';

  @override
  String get recurringBill => 'आवर्ती बिल';

  @override
  String get monthly => 'मासिक';

  @override
  String get quarterly => 'त्रैमासिक';

  @override
  String get halfYearly => 'अर्धवार्षिक';

  @override
  String get yearly => 'वार्षिक';

  @override
  String get greatAllCaughtUp => 'बहुत बढ़िया! आप सब कुछ पूरा कर चुके हैं';

  @override
  String get noUrgentTasksOrBills => 'आज कोई तत्काल कार्य या बिल नहीं';

  @override
  String get heresSuggestion => 'जी हां, यहां एक सुझाव है:';
}
