// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'LifeLoop';

  @override
  String get goodMorning => 'Good Morning';

  @override
  String get goodAfternoon => 'Good Afternoon';

  @override
  String get goodEvening => 'Good Evening';

  @override
  String get todaysTasks => 'Today\'s Tasks';

  @override
  String get upcomingBills => 'Upcoming Bills';

  @override
  String get planMyDay => 'Plan my day';

  @override
  String get tasks => 'Tasks';

  @override
  String get bills => 'Bills & Payments';

  @override
  String get settings => 'Settings';

  @override
  String get all => 'All';

  @override
  String get today => 'Today';

  @override
  String get pending => 'Pending';

  @override
  String get completed => 'Completed';

  @override
  String get overdue => 'Overdue';

  @override
  String get paid => 'Paid';

  @override
  String get upcoming => 'Upcoming';

  @override
  String get addTask => 'Add Task';

  @override
  String get addBill => 'Add Bill';

  @override
  String get editTask => 'Edit Task';

  @override
  String get editBill => 'Edit Bill';

  @override
  String get taskTitle => 'Task Title';

  @override
  String get billTitle => 'Bill Title';

  @override
  String get description => 'Description';

  @override
  String get amount => 'Amount';

  @override
  String get dueDate => 'Due Date';

  @override
  String get priority => 'Priority';

  @override
  String get category => 'Category';

  @override
  String get status => 'Status';

  @override
  String get low => 'Low';

  @override
  String get medium => 'Medium';

  @override
  String get high => 'High';

  @override
  String get urgent => 'Urgent';

  @override
  String get work => 'Work';

  @override
  String get personal => 'Personal';

  @override
  String get shopping => 'Shopping';

  @override
  String get health => 'Health';

  @override
  String get finance => 'Finance';

  @override
  String get home => 'Home';

  @override
  String get other => 'Other';

  @override
  String get electricity => 'Electricity';

  @override
  String get water => 'Water';

  @override
  String get gas => 'Gas';

  @override
  String get internet => 'Internet';

  @override
  String get mobile => 'Mobile';

  @override
  String get dth => 'DTH';

  @override
  String get rent => 'Rent';

  @override
  String get insurance => 'Insurance';

  @override
  String get loan => 'Loan';

  @override
  String get save => 'Save';

  @override
  String get cancel => 'Cancel';

  @override
  String get delete => 'Delete';

  @override
  String get edit => 'Edit';

  @override
  String get close => 'Close';

  @override
  String get search => 'Search';

  @override
  String get filter => 'Filter';

  @override
  String get noTasksToday => 'No tasks for today';

  @override
  String get noUpcomingBills => 'No upcoming bills';

  @override
  String get addTaskToGetStarted => 'Add a task to get started';

  @override
  String get addBillToGetStarted => 'Tap + to add a new bill';

  @override
  String get markAsPaid => 'Mark as Paid';

  @override
  String get markAsComplete => 'Mark as Complete';

  @override
  String get language => 'Language';

  @override
  String get selectLanguage => 'Select Language';

  @override
  String get statistics => 'Statistics';

  @override
  String get export => 'Export';

  @override
  String get taskAddedSuccessfully => 'Task added successfully!';

  @override
  String get billAddedSuccessfully => 'Bill added successfully!';

  @override
  String get taskUpdatedSuccessfully => 'Task updated successfully!';

  @override
  String get billUpdatedSuccessfully => 'Bill updated successfully!';

  @override
  String get deleteTaskConfirmation => 'Are you sure you want to delete this task?';

  @override
  String get deleteBillConfirmation => 'Are you sure you want to delete this bill?';

  @override
  String get recurringBill => 'Recurring Bill';

  @override
  String get monthly => 'Monthly';

  @override
  String get quarterly => 'Quarterly';

  @override
  String get halfYearly => 'Half Yearly';

  @override
  String get yearly => 'Yearly';

  @override
  String get greatAllCaughtUp => 'Great! You\'re all caught up';

  @override
  String get noUrgentTasksOrBills => 'No urgent tasks or bills for today';

  @override
  String get heresSuggestion => 'Sure, here\'s a suggestion:';
}
