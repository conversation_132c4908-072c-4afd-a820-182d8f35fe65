import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_hi.dart';
import 'app_localizations_ta.dart';
import 'app_localizations_te.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('hi'),
    Locale('ta'),
    Locale('te')
  ];

  /// The title of the application
  ///
  /// In en, this message translates to:
  /// **'LifeLoop'**
  String get appTitle;

  /// Morning greeting
  ///
  /// In en, this message translates to:
  /// **'Good Morning'**
  String get goodMorning;

  /// Afternoon greeting
  ///
  /// In en, this message translates to:
  /// **'Good Afternoon'**
  String get goodAfternoon;

  /// Evening greeting
  ///
  /// In en, this message translates to:
  /// **'Good Evening'**
  String get goodEvening;

  /// Header for today's tasks section
  ///
  /// In en, this message translates to:
  /// **'Today\'s Tasks'**
  String get todaysTasks;

  /// Header for upcoming bills section
  ///
  /// In en, this message translates to:
  /// **'Upcoming Bills'**
  String get upcomingBills;

  /// Header for day planning section
  ///
  /// In en, this message translates to:
  /// **'Plan my day'**
  String get planMyDay;

  /// Tasks screen title
  ///
  /// In en, this message translates to:
  /// **'Tasks'**
  String get tasks;

  /// Bills screen title
  ///
  /// In en, this message translates to:
  /// **'Bills & Payments'**
  String get bills;

  /// Settings screen title
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// All items filter
  ///
  /// In en, this message translates to:
  /// **'All'**
  String get all;

  /// Today filter
  ///
  /// In en, this message translates to:
  /// **'Today'**
  String get today;

  /// Pending status
  ///
  /// In en, this message translates to:
  /// **'Pending'**
  String get pending;

  /// Completed status
  ///
  /// In en, this message translates to:
  /// **'Completed'**
  String get completed;

  /// Overdue status
  ///
  /// In en, this message translates to:
  /// **'Overdue'**
  String get overdue;

  /// Paid status
  ///
  /// In en, this message translates to:
  /// **'Paid'**
  String get paid;

  /// Upcoming status
  ///
  /// In en, this message translates to:
  /// **'Upcoming'**
  String get upcoming;

  /// Add task button
  ///
  /// In en, this message translates to:
  /// **'Add Task'**
  String get addTask;

  /// Add bill button
  ///
  /// In en, this message translates to:
  /// **'Add Bill'**
  String get addBill;

  /// Edit task dialog title
  ///
  /// In en, this message translates to:
  /// **'Edit Task'**
  String get editTask;

  /// Edit bill dialog title
  ///
  /// In en, this message translates to:
  /// **'Edit Bill'**
  String get editBill;

  /// Task title field label
  ///
  /// In en, this message translates to:
  /// **'Task Title'**
  String get taskTitle;

  /// Bill title field label
  ///
  /// In en, this message translates to:
  /// **'Bill Title'**
  String get billTitle;

  /// Description field label
  ///
  /// In en, this message translates to:
  /// **'Description'**
  String get description;

  /// Amount field label
  ///
  /// In en, this message translates to:
  /// **'Amount'**
  String get amount;

  /// Due date field label
  ///
  /// In en, this message translates to:
  /// **'Due Date'**
  String get dueDate;

  /// Priority field label
  ///
  /// In en, this message translates to:
  /// **'Priority'**
  String get priority;

  /// Category field label
  ///
  /// In en, this message translates to:
  /// **'Category'**
  String get category;

  /// Status field label
  ///
  /// In en, this message translates to:
  /// **'Status'**
  String get status;

  /// Low priority
  ///
  /// In en, this message translates to:
  /// **'Low'**
  String get low;

  /// Medium priority
  ///
  /// In en, this message translates to:
  /// **'Medium'**
  String get medium;

  /// High priority
  ///
  /// In en, this message translates to:
  /// **'High'**
  String get high;

  /// Urgent priority
  ///
  /// In en, this message translates to:
  /// **'Urgent'**
  String get urgent;

  /// Work category
  ///
  /// In en, this message translates to:
  /// **'Work'**
  String get work;

  /// Personal category
  ///
  /// In en, this message translates to:
  /// **'Personal'**
  String get personal;

  /// Shopping category
  ///
  /// In en, this message translates to:
  /// **'Shopping'**
  String get shopping;

  /// Health category
  ///
  /// In en, this message translates to:
  /// **'Health'**
  String get health;

  /// Finance category
  ///
  /// In en, this message translates to:
  /// **'Finance'**
  String get finance;

  /// Home category
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get home;

  /// Other category
  ///
  /// In en, this message translates to:
  /// **'Other'**
  String get other;

  /// Electricity bill type
  ///
  /// In en, this message translates to:
  /// **'Electricity'**
  String get electricity;

  /// Water bill type
  ///
  /// In en, this message translates to:
  /// **'Water'**
  String get water;

  /// Gas bill type
  ///
  /// In en, this message translates to:
  /// **'Gas'**
  String get gas;

  /// Internet bill type
  ///
  /// In en, this message translates to:
  /// **'Internet'**
  String get internet;

  /// Mobile bill type
  ///
  /// In en, this message translates to:
  /// **'Mobile'**
  String get mobile;

  /// DTH bill type
  ///
  /// In en, this message translates to:
  /// **'DTH'**
  String get dth;

  /// Rent bill type
  ///
  /// In en, this message translates to:
  /// **'Rent'**
  String get rent;

  /// Insurance bill type
  ///
  /// In en, this message translates to:
  /// **'Insurance'**
  String get insurance;

  /// Loan bill type
  ///
  /// In en, this message translates to:
  /// **'Loan'**
  String get loan;

  /// Save button
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// Cancel button
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// Delete button
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// Edit button
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get edit;

  /// Close button
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get close;

  /// Search button
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get search;

  /// Filter button
  ///
  /// In en, this message translates to:
  /// **'Filter'**
  String get filter;

  /// Empty state message for today's tasks
  ///
  /// In en, this message translates to:
  /// **'No tasks for today'**
  String get noTasksToday;

  /// Empty state message for upcoming bills
  ///
  /// In en, this message translates to:
  /// **'No upcoming bills'**
  String get noUpcomingBills;

  /// Empty state helper text for tasks
  ///
  /// In en, this message translates to:
  /// **'Add a task to get started'**
  String get addTaskToGetStarted;

  /// Empty state helper text for bills
  ///
  /// In en, this message translates to:
  /// **'Tap + to add a new bill'**
  String get addBillToGetStarted;

  /// Mark bill as paid button
  ///
  /// In en, this message translates to:
  /// **'Mark as Paid'**
  String get markAsPaid;

  /// Mark task as complete button
  ///
  /// In en, this message translates to:
  /// **'Mark as Complete'**
  String get markAsComplete;

  /// Language setting
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// Language selection dialog title
  ///
  /// In en, this message translates to:
  /// **'Select Language'**
  String get selectLanguage;

  /// Statistics menu item
  ///
  /// In en, this message translates to:
  /// **'Statistics'**
  String get statistics;

  /// Export menu item
  ///
  /// In en, this message translates to:
  /// **'Export'**
  String get export;

  /// Success message for adding task
  ///
  /// In en, this message translates to:
  /// **'Task added successfully!'**
  String get taskAddedSuccessfully;

  /// Success message for adding bill
  ///
  /// In en, this message translates to:
  /// **'Bill added successfully!'**
  String get billAddedSuccessfully;

  /// Success message for updating task
  ///
  /// In en, this message translates to:
  /// **'Task updated successfully!'**
  String get taskUpdatedSuccessfully;

  /// Success message for updating bill
  ///
  /// In en, this message translates to:
  /// **'Bill updated successfully!'**
  String get billUpdatedSuccessfully;

  /// Confirmation message for deleting task
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this task?'**
  String get deleteTaskConfirmation;

  /// Confirmation message for deleting bill
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this bill?'**
  String get deleteBillConfirmation;

  /// Recurring bill option
  ///
  /// In en, this message translates to:
  /// **'Recurring Bill'**
  String get recurringBill;

  /// Monthly recurrence
  ///
  /// In en, this message translates to:
  /// **'Monthly'**
  String get monthly;

  /// Quarterly recurrence
  ///
  /// In en, this message translates to:
  /// **'Quarterly'**
  String get quarterly;

  /// Half yearly recurrence
  ///
  /// In en, this message translates to:
  /// **'Half Yearly'**
  String get halfYearly;

  /// Yearly recurrence
  ///
  /// In en, this message translates to:
  /// **'Yearly'**
  String get yearly;

  /// Day planner message when no urgent tasks
  ///
  /// In en, this message translates to:
  /// **'Great! You\'re all caught up'**
  String get greatAllCaughtUp;

  /// Day planner subtitle when no urgent items
  ///
  /// In en, this message translates to:
  /// **'No urgent tasks or bills for today'**
  String get noUrgentTasksOrBills;

  /// Day planner suggestion intro
  ///
  /// In en, this message translates to:
  /// **'Sure, here\'s a suggestion:'**
  String get heresSuggestion;
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['en', 'hi', 'ta', 'te'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {


  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en': return AppLocalizationsEn();
    case 'hi': return AppLocalizationsHi();
    case 'ta': return AppLocalizationsTa();
    case 'te': return AppLocalizationsTe();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}
