// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Tamil (`ta`).
class AppLocalizationsTa extends AppLocalizations {
  AppLocalizationsTa([String locale = 'ta']) : super(locale);

  @override
  String get appTitle => 'லைஃப்லூப்';

  @override
  String get goodMorning => 'காலை வணக்கம்';

  @override
  String get goodAfternoon => 'வணக்கம்';

  @override
  String get goodEvening => 'மாலை வணக்கம்';

  @override
  String get todaysTasks => 'இன்றைய பணிகள்';

  @override
  String get upcomingBills => 'வரவிருக்கும் பில்கள்';

  @override
  String get planMyDay => 'என் நாளைத் திட்டமிடு';

  @override
  String get tasks => 'பணிகள்';

  @override
  String get bills => 'பில்கள் மற்றும் பணம் செலுத்துதல்';

  @override
  String get settings => 'அமைப்புகள்';

  @override
  String get all => 'அனைத்தும்';

  @override
  String get today => 'இன்று';

  @override
  String get pending => 'நிலுவையில்';

  @override
  String get completed => 'முடிந்தது';

  @override
  String get overdue => 'தாமதமானது';

  @override
  String get paid => 'செலுத்தப்பட்டது';

  @override
  String get upcoming => 'வரவிருக்கும்';

  @override
  String get addTask => 'பணி சேர்க்கவும்';

  @override
  String get addBill => 'பில் சேர்க்கவும்';

  @override
  String get editTask => 'பணியைத் திருத்தவும்';

  @override
  String get editBill => 'பில்லைத் திருத்தவும்';

  @override
  String get taskTitle => 'பணி தலைப்பு';

  @override
  String get billTitle => 'பில் தலைப்பு';

  @override
  String get description => 'விளக்கம்';

  @override
  String get amount => 'தொகை';

  @override
  String get dueDate => 'செலுத்த வேண்டிய தேதி';

  @override
  String get priority => 'முன்னுரிமை';

  @override
  String get category => 'வகை';

  @override
  String get status => 'நிலை';

  @override
  String get low => 'குறைவு';

  @override
  String get medium => 'நடுத்தர';

  @override
  String get high => 'உயர்';

  @override
  String get urgent => 'அவசர';

  @override
  String get work => 'வேலை';

  @override
  String get personal => 'தனிப்பட்ட';

  @override
  String get shopping => 'ஷாப்பிங்';

  @override
  String get health => 'ஆரோக்கியம்';

  @override
  String get finance => 'நிதி';

  @override
  String get home => 'வீடு';

  @override
  String get other => 'மற்றவை';

  @override
  String get electricity => 'மின்சாரம்';

  @override
  String get water => 'தண்ணீர்';

  @override
  String get gas => 'எரிவாயு';

  @override
  String get internet => 'இணையம்';

  @override
  String get mobile => 'மொபைல்';

  @override
  String get dth => 'டிடிஎச்';

  @override
  String get rent => 'வாடகை';

  @override
  String get insurance => 'காப்பீடு';

  @override
  String get loan => 'கடன்';

  @override
  String get save => 'சேமிக்கவும்';

  @override
  String get cancel => 'ரத்து செய்யவும்';

  @override
  String get delete => 'நீக்கவும்';

  @override
  String get edit => 'திருத்தவும்';

  @override
  String get close => 'மூடவும்';

  @override
  String get search => 'தேடவும்';

  @override
  String get filter => 'வடிகட்டவும்';

  @override
  String get noTasksToday => 'இன்று பணிகள் இல்லை';

  @override
  String get noUpcomingBills => 'வரவிருக்கும் பில்கள் இல்லை';

  @override
  String get addTaskToGetStarted => 'தொடங்க ஒரு பணியைச் சேர்க்கவும்';

  @override
  String get addBillToGetStarted => 'புதிய பில் சேர்க்க + ஐ அழுத்தவும்';

  @override
  String get markAsPaid => 'செலுத்தியதாகக் குறிக்கவும்';

  @override
  String get markAsComplete => 'முடிந்ததாகக் குறிக்கவும்';

  @override
  String get language => 'மொழி';

  @override
  String get selectLanguage => 'மொழியைத் தேர்ந்தெடுக்கவும்';

  @override
  String get statistics => 'புள்ளிவிவரங்கள்';

  @override
  String get export => 'ஏற்றுமதி';

  @override
  String get taskAddedSuccessfully => 'பணி வெற்றிகரமாக சேர்க்கப்பட்டது!';

  @override
  String get billAddedSuccessfully => 'பில் வெற்றிகரமாக சேர்க்கப்பட்டது!';

  @override
  String get taskUpdatedSuccessfully => 'பணி வெற்றிகரமாக புதுப்பிக்கப்பட்டது!';

  @override
  String get billUpdatedSuccessfully => 'பில் வெற்றிகரமாக புதுப்பிக்கப்பட்டது!';

  @override
  String get deleteTaskConfirmation => 'இந்த பணியை நிச்சயமாக நீக்க விரும்புகிறீர்களா?';

  @override
  String get deleteBillConfirmation => 'இந்த பில்லை நிச்சயமாக நீக்க விரும்புகிறீர்களா?';

  @override
  String get recurringBill => 'மீண்டும் வரும் பில்';

  @override
  String get monthly => 'மாதாந்திர';

  @override
  String get quarterly => 'காலாண்டு';

  @override
  String get halfYearly => 'அரையாண்டு';

  @override
  String get yearly => 'வருடாந்திர';

  @override
  String get greatAllCaughtUp => 'அருமை! நீங்கள் எல்லாவற்றையும் முடித்துவிட்டீர்கள்';

  @override
  String get noUrgentTasksOrBills => 'இன்று அவசர பணிகள் அல்லது பில்கள் இல்லை';

  @override
  String get heresSuggestion => 'சரி, இதோ ஒரு பரிந்துரை:';
}
