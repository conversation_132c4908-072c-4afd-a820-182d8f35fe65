import 'dart:math';
import '../models/task.dart';
import '../models/bill.dart';
import '../services/localization_service.dart';

class AIPlanningService {
  static const int maxSuggestions = 5;
  static const int maxDailyTasks = 8;
  
  /// Generate intelligent day planning suggestions based on tasks, bills, and user patterns
  static List<DayPlanningSuggestion> generateDayPlan({
    required List<Task> tasks,
    required List<Bill> bills,
    required DateTime targetDate,
    Map<String, dynamic>? userPreferences,
    List<Task>? completionHistory,
  }) {
    final suggestions = <DayPlanningSuggestion>[];
    final now = DateTime.now();
    final isToday = _isSameDay(targetDate, now);
    
    // Analyze current situation
    final analysis = _analyzeCurrentSituation(tasks, bills, targetDate);
    
    // Generate priority-based suggestions
    suggestions.addAll(_generatePrioritySuggestions(analysis, isToday));
    
    // Generate time-based suggestions
    suggestions.addAll(_generateTimeSuggestions(analysis, targetDate));
    
    // Generate productivity suggestions
    suggestions.addAll(_generateProductivitySuggestions(analysis, completionHistory));
    
    // Generate wellness suggestions
    suggestions.addAll(_generateWellnessSuggestions(analysis, targetDate));
    
    // Generate bill-related suggestions
    suggestions.addAll(_generateBillSuggestions(analysis));
    
    // Sort by priority and limit results
    suggestions.sort((a, b) => b.priority.compareTo(a.priority));
    
    return suggestions.take(maxSuggestions).toList();
  }
  
  static SituationAnalysis _analyzeCurrentSituation(
    List<Task> tasks, 
    List<Bill> bills, 
    DateTime targetDate
  ) {
    final pendingTasks = tasks.where((t) => !t.isCompleted).toList();
    final overdueTasks = pendingTasks.where((t) => 
      t.dueDate != null && t.dueDate!.isBefore(DateTime.now())
    ).toList();
    
    final urgentTasks = pendingTasks.where((t) => 
      t.priority == TaskPriority.urgent
    ).toList();
    
    final highPriorityTasks = pendingTasks.where((t) => 
      t.priority == TaskPriority.high
    ).toList();
    
    final todayTasks = pendingTasks.where((t) => 
      t.dueDate != null && _isSameDay(t.dueDate!, targetDate)
    ).toList();
    
    final upcomingTasks = pendingTasks.where((t) => 
      t.dueDate != null && 
      t.dueDate!.isAfter(targetDate) &&
      t.dueDate!.isBefore(targetDate.add(const Duration(days: 7)))
    ).toList();
    
    final billsDueToday = bills.where((b) => 
      b.status == BillStatus.pending && _isSameDay(b.dueDate, targetDate)
    ).toList();
    
    final overdueBills = bills.where((b) => 
      b.status == BillStatus.pending && b.dueDate.isBefore(DateTime.now())
    ).toList();
    
    final upcomingBills = bills.where((b) => 
      b.status == BillStatus.pending &&
      b.dueDate.isAfter(targetDate) &&
      b.dueDate.isBefore(targetDate.add(const Duration(days: 3)))
    ).toList();
    
    return SituationAnalysis(
      totalTasks: tasks.length,
      pendingTasks: pendingTasks,
      overdueTasks: overdueTasks,
      urgentTasks: urgentTasks,
      highPriorityTasks: highPriorityTasks,
      todayTasks: todayTasks,
      upcomingTasks: upcomingTasks,
      billsDueToday: billsDueToday,
      overdueBills: overdueBills,
      upcomingBills: upcomingBills,
      workload: _calculateWorkload(pendingTasks),
      stressLevel: _calculateStressLevel(overdueTasks, urgentTasks, overdueBills),
    );
  }
  
  static List<DayPlanningSuggestion> _generatePrioritySuggestions(
    SituationAnalysis analysis, 
    bool isToday
  ) {
    final suggestions = <DayPlanningSuggestion>[];
    
    // Handle overdue items first
    if (analysis.overdueTasks.isNotEmpty) {
      final task = analysis.overdueTasks.first;
      suggestions.add(DayPlanningSuggestion(
        id: 'overdue_task_${task.id}',
        type: SuggestionType.urgent,
        title: 'Complete overdue task',
        description: 'Focus on "${task.title}" - it\'s overdue and needs immediate attention',
        priority: 10,
        estimatedTime: _estimateTaskTime(task),
        relatedTaskId: task.id,
        icon: '🚨',
      ));
    }
    
    if (analysis.overdueBills.isNotEmpty) {
      final bill = analysis.overdueBills.first;
      suggestions.add(DayPlanningSuggestion(
        id: 'overdue_bill_${bill.id}',
        type: SuggestionType.urgent,
        title: 'Pay overdue bill',
        description: 'Pay ${bill.title} - it\'s overdue and may incur penalties',
        priority: 9,
        estimatedTime: 10,
        relatedBillId: bill.id,
        icon: '💸',
      ));
    }
    
    // Handle urgent tasks
    if (analysis.urgentTasks.isNotEmpty) {
      final task = analysis.urgentTasks.first;
      suggestions.add(DayPlanningSuggestion(
        id: 'urgent_task_${task.id}',
        type: SuggestionType.priority,
        title: 'Handle urgent task',
        description: 'Work on "${task.title}" - marked as urgent priority',
        priority: 8,
        estimatedTime: _estimateTaskTime(task),
        relatedTaskId: task.id,
        icon: '⚡',
      ));
    }
    
    // Handle bills due today
    if (analysis.billsDueToday.isNotEmpty) {
      final bill = analysis.billsDueToday.first;
      suggestions.add(DayPlanningSuggestion(
        id: 'bill_due_today_${bill.id}',
        type: SuggestionType.deadline,
        title: 'Pay bill due today',
        description: 'Pay ${bill.title} - due today',
        priority: 7,
        estimatedTime: 10,
        relatedBillId: bill.id,
        icon: '📅',
      ));
    }
    
    return suggestions;
  }
  
  static List<DayPlanningSuggestion> _generateTimeSuggestions(
    SituationAnalysis analysis, 
    DateTime targetDate
  ) {
    final suggestions = <DayPlanningSuggestion>[];
    final hour = DateTime.now().hour;
    
    // Morning suggestions (6-11 AM)
    if (hour >= 6 && hour < 12) {
      if (analysis.highPriorityTasks.isNotEmpty) {
        suggestions.add(DayPlanningSuggestion(
          id: 'morning_focus',
          type: SuggestionType.productivity,
          title: 'Morning focus session',
          description: 'Start with high-priority tasks while your energy is peak',
          priority: 6,
          estimatedTime: 90,
          icon: '🌅',
        ));
      }
    }
    
    // Afternoon suggestions (12-5 PM)
    else if (hour >= 12 && hour < 17) {
      if (analysis.pendingTasks.length > 3) {
        suggestions.add(DayPlanningSuggestion(
          id: 'afternoon_batch',
          type: SuggestionType.productivity,
          title: 'Batch similar tasks',
          description: 'Group similar tasks together for better efficiency',
          priority: 5,
          estimatedTime: 60,
          icon: '📋',
        ));
      }
    }
    
    // Evening suggestions (5-9 PM)
    else if (hour >= 17 && hour < 21) {
      if (analysis.upcomingTasks.isNotEmpty) {
        suggestions.add(DayPlanningSuggestion(
          id: 'evening_prep',
          type: SuggestionType.planning,
          title: 'Prepare for tomorrow',
          description: 'Review upcoming tasks and plan tomorrow\'s priorities',
          priority: 4,
          estimatedTime: 30,
          icon: '🌆',
        ));
      }
    }
    
    return suggestions;
  }
  
  static List<DayPlanningSuggestion> _generateProductivitySuggestions(
    SituationAnalysis analysis,
    List<Task>? completionHistory,
  ) {
    final suggestions = <DayPlanningSuggestion>[];
    
    // Workload management
    if (analysis.workload > 0.8) {
      suggestions.add(DayPlanningSuggestion(
        id: 'reduce_workload',
        type: SuggestionType.wellness,
        title: 'Consider reducing workload',
        description: 'You have a heavy workload today. Consider postponing non-urgent tasks',
        priority: 5,
        estimatedTime: 15,
        icon: '⚖️',
      ));
    }
    
    // Focus time suggestion
    if (analysis.pendingTasks.length >= 3) {
      suggestions.add(DayPlanningSuggestion(
        id: 'focus_time',
        type: SuggestionType.productivity,
        title: 'Schedule focus time',
        description: 'Block 2 hours for deep work on your most important tasks',
        priority: 6,
        estimatedTime: 120,
        icon: '🎯',
      ));
    }
    
    return suggestions;
  }
  
  static List<DayPlanningSuggestion> _generateWellnessSuggestions(
    SituationAnalysis analysis,
    DateTime targetDate,
  ) {
    final suggestions = <DayPlanningSuggestion>[];
    
    // Stress management
    if (analysis.stressLevel > 0.7) {
      suggestions.add(DayPlanningSuggestion(
        id: 'stress_break',
        type: SuggestionType.wellness,
        title: 'Take a stress break',
        description: 'High stress detected. Take a 15-minute break to recharge',
        priority: 7,
        estimatedTime: 15,
        icon: '🧘',
      ));
    }
    
    // Regular breaks
    if (analysis.pendingTasks.length > 5) {
      suggestions.add(DayPlanningSuggestion(
        id: 'regular_breaks',
        type: SuggestionType.wellness,
        title: 'Schedule regular breaks',
        description: 'Take 5-minute breaks between tasks to maintain productivity',
        priority: 3,
        estimatedTime: 5,
        icon: '☕',
      ));
    }
    
    return suggestions;
  }
  
  static List<DayPlanningSuggestion> _generateBillSuggestions(
    SituationAnalysis analysis,
  ) {
    final suggestions = <DayPlanningSuggestion>[];
    
    // Upcoming bills reminder
    if (analysis.upcomingBills.isNotEmpty) {
      suggestions.add(DayPlanningSuggestion(
        id: 'upcoming_bills',
        type: SuggestionType.reminder,
        title: 'Upcoming bill reminder',
        description: 'You have ${analysis.upcomingBills.length} bills due in the next 3 days',
        priority: 4,
        estimatedTime: 5,
        icon: '📋',
      ));
    }
    
    return suggestions;
  }
  
  static double _calculateWorkload(List<Task> pendingTasks) {
    if (pendingTasks.isEmpty) return 0.0;
    
    double totalWeight = 0.0;
    for (final task in pendingTasks) {
      switch (task.priority) {
        case TaskPriority.urgent:
          totalWeight += 4.0;
          break;
        case TaskPriority.high:
          totalWeight += 3.0;
          break;
        case TaskPriority.medium:
          totalWeight += 2.0;
          break;
        case TaskPriority.low:
          totalWeight += 1.0;
          break;
      }
    }
    
    return min(totalWeight / (maxDailyTasks * 2.5), 1.0);
  }
  
  static double _calculateStressLevel(
    List<Task> overdueTasks,
    List<Task> urgentTasks,
    List<Bill> overdueBills,
  ) {
    double stress = 0.0;
    stress += overdueTasks.length * 0.3;
    stress += urgentTasks.length * 0.2;
    stress += overdueBills.length * 0.25;
    
    return min(stress, 1.0);
  }
  
  static int _estimateTaskTime(Task task) {
    // Simple heuristic based on task complexity
    int baseTime = 30; // 30 minutes default
    
    if (task.description != null && task.description!.length > 100) {
      baseTime += 30; // Complex tasks take longer
    }
    
    switch (task.priority) {
      case TaskPriority.urgent:
      case TaskPriority.high:
        baseTime += 15; // High priority tasks often more complex
        break;
      default:
        break;
    }
    
    return baseTime;
  }
  
  static bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }
}

class SituationAnalysis {
  final int totalTasks;
  final List<Task> pendingTasks;
  final List<Task> overdueTasks;
  final List<Task> urgentTasks;
  final List<Task> highPriorityTasks;
  final List<Task> todayTasks;
  final List<Task> upcomingTasks;
  final List<Bill> billsDueToday;
  final List<Bill> overdueBills;
  final List<Bill> upcomingBills;
  final double workload; // 0.0 to 1.0
  final double stressLevel; // 0.0 to 1.0
  
  SituationAnalysis({
    required this.totalTasks,
    required this.pendingTasks,
    required this.overdueTasks,
    required this.urgentTasks,
    required this.highPriorityTasks,
    required this.todayTasks,
    required this.upcomingTasks,
    required this.billsDueToday,
    required this.overdueBills,
    required this.upcomingBills,
    required this.workload,
    required this.stressLevel,
  });
}

class DayPlanningSuggestion {
  final String id;
  final SuggestionType type;
  final String title;
  final String description;
  final int priority; // 1-10, higher is more important
  final int estimatedTime; // in minutes
  final int? relatedTaskId;
  final int? relatedBillId;
  final String icon;
  
  DayPlanningSuggestion({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.priority,
    required this.estimatedTime,
    this.relatedTaskId,
    this.relatedBillId,
    required this.icon,
  });
}

enum SuggestionType {
  urgent,
  priority,
  deadline,
  productivity,
  wellness,
  planning,
  reminder,
}
