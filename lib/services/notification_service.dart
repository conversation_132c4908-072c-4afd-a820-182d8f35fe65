import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz;
import '../models/task.dart';
import '../models/bill.dart';
import '../services/localization_service.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  static NotificationService get instance => _instance;

  final FlutterLocalNotificationsPlugin _notifications = FlutterLocalNotificationsPlugin();
  bool _isInitialized = false;

  // Notification channels
  static const String taskChannelId = 'task_notifications';
  static const String billChannelId = 'bill_notifications';
  static const String reminderChannelId = 'reminder_notifications';
  static const String aiChannelId = 'ai_suggestions';

  Future<void> initialize() async {
    if (_isInitialized) return;

    // Initialize timezone
    tz.initializeTimeZones();

    // Android initialization
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    
    // iOS initialization
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _notifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Request permissions
    await _requestPermissions();

    _isInitialized = true;
  }

  Future<void> _requestPermissions() async {
    if (defaultTargetPlatform == TargetPlatform.iOS) {
      await _notifications
          .resolvePlatformSpecificImplementation<IOSFlutterLocalNotificationsPlugin>()
          ?.requestPermissions(
            alert: true,
            badge: true,
            sound: true,
          );
    } else if (defaultTargetPlatform == TargetPlatform.android) {
      final androidImplementation = _notifications.resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin>();
      await androidImplementation?.requestNotificationsPermission();
    }
  }

  void _onNotificationTapped(NotificationResponse response) {
    final payload = response.payload;
    if (payload != null) {
      _handleNotificationPayload(payload);
    }
  }

  void _handleNotificationPayload(String payload) {
    // Parse payload and handle navigation
    final parts = payload.split('|');
    if (parts.length >= 2) {
      final type = parts[0];
      final id = parts[1];
      
      switch (type) {
        case 'task':
          // Navigate to task details
          debugPrint('Navigate to task: $id');
          break;
        case 'bill':
          // Navigate to bill details
          debugPrint('Navigate to bill: $id');
          break;
        case 'reminder':
          // Handle reminder
          debugPrint('Handle reminder: $id');
          break;
      }
    }
  }

  // Task Notifications
  Future<void> scheduleTaskReminder(Task task) async {
    if (!_isInitialized) await initialize();
    if (task.dueDate == null) return;

    final notificationId = _generateTaskNotificationId(task.id!);
    
    // Schedule notification 1 hour before due time
    final reminderTime = task.dueDate!.subtract(const Duration(hours: 1));
    
    if (reminderTime.isAfter(DateTime.now())) {
      await _scheduleNotification(
        id: notificationId,
        title: 'Task Reminder',
        body: 'Task "${task.title}" is due in 1 hour',
        scheduledDate: reminderTime,
        payload: 'task|${task.id}',
        channelId: taskChannelId,
        channelName: 'Task Reminders',
        channelDescription: 'Notifications for task due dates',
      );
    }

    // Schedule notification at due time if not completed
    if (!task.isCompleted) {
      await _scheduleNotification(
        id: notificationId + 1,
        title: 'Task Due Now',
        body: 'Task "${task.title}" is due now!',
        scheduledDate: task.dueDate!,
        payload: 'task|${task.id}',
        channelId: taskChannelId,
        channelName: 'Task Reminders',
        channelDescription: 'Notifications for task due dates',
        priority: Priority.high,
      );
    }
  }

  Future<void> scheduleTaskDeadlineAlert(Task task) async {
    if (!_isInitialized) await initialize();
    if (task.dueDate == null) return;

    final notificationId = _generateTaskNotificationId(task.id!) + 10;
    
    // Schedule notification 1 day before due date
    final alertTime = DateTime(
      task.dueDate!.year,
      task.dueDate!.month,
      task.dueDate!.day - 1,
      9, // 9 AM
    );
    
    if (alertTime.isAfter(DateTime.now())) {
      await _scheduleNotification(
        id: notificationId,
        title: 'Task Deadline Tomorrow',
        body: 'Don\'t forget: "${task.title}" is due tomorrow',
        scheduledDate: alertTime,
        payload: 'task|${task.id}',
        channelId: taskChannelId,
        channelName: 'Task Reminders',
        channelDescription: 'Notifications for task due dates',
      );
    }
  }

  // Bill Notifications
  Future<void> scheduleBillReminder(Bill bill) async {
    if (!_isInitialized) await initialize();

    final notificationId = _generateBillNotificationId(bill.id!);
    
    // Schedule notification 3 days before due date
    final reminderTime = DateTime(
      bill.dueDate.year,
      bill.dueDate.month,
      bill.dueDate.day - 3,
      10, // 10 AM
    );
    
    if (reminderTime.isAfter(DateTime.now())) {
      await _scheduleNotification(
        id: notificationId,
        title: 'Bill Reminder',
        body: '${bill.title} bill is due in 3 days (₹${bill.amount?.toStringAsFixed(0) ?? 'N/A'})',
        scheduledDate: reminderTime,
        payload: 'bill|${bill.id}',
        channelId: billChannelId,
        channelName: 'Bill Reminders',
        channelDescription: 'Notifications for bill due dates',
      );
    }

    // Schedule notification 1 day before due date
    final urgentReminderTime = DateTime(
      bill.dueDate.year,
      bill.dueDate.month,
      bill.dueDate.day - 1,
      18, // 6 PM
    );
    
    if (urgentReminderTime.isAfter(DateTime.now())) {
      await _scheduleNotification(
        id: notificationId + 1,
        title: 'Bill Due Tomorrow',
        body: 'Pay ${bill.title} bill tomorrow (₹${bill.amount?.toStringAsFixed(0) ?? 'N/A'})',
        scheduledDate: urgentReminderTime,
        payload: 'bill|${bill.id}',
        channelId: billChannelId,
        channelName: 'Bill Reminders',
        channelDescription: 'Notifications for bill due dates',
        priority: Priority.high,
      );
    }

    // Schedule notification on due date
    final dueDateReminder = DateTime(
      bill.dueDate.year,
      bill.dueDate.month,
      bill.dueDate.day,
      9, // 9 AM
    );
    
    if (dueDateReminder.isAfter(DateTime.now())) {
      await _scheduleNotification(
        id: notificationId + 2,
        title: 'Bill Due Today',
        body: '${bill.title} bill is due today! (₹${bill.amount?.toStringAsFixed(0) ?? 'N/A'})',
        scheduledDate: dueDateReminder,
        payload: 'bill|${bill.id}',
        channelId: billChannelId,
        channelName: 'Bill Reminders',
        channelDescription: 'Notifications for bill due dates',
        priority: Priority.max,
      );
    }
  }

  // Daily Planning Notifications
  Future<void> scheduleDailyPlanningReminder() async {
    if (!_isInitialized) await initialize();

    // Schedule daily planning reminder at 8 AM
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    final planningTime = DateTime(tomorrow.year, tomorrow.month, tomorrow.day, 8);

    await _scheduleNotification(
      id: 9999, // Fixed ID for daily planning
      title: 'Plan Your Day',
      body: 'Good morning! Take a moment to review your tasks and priorities for today.',
      scheduledDate: planningTime,
      payload: 'reminder|daily_planning',
      channelId: reminderChannelId,
      channelName: 'Daily Reminders',
      channelDescription: 'Daily planning and productivity reminders',
      recurring: true,
    );
  }

  // AI Suggestion Notifications
  Future<void> scheduleAISuggestionNotification(String suggestion) async {
    if (!_isInitialized) await initialize();

    await _showImmediateNotification(
      id: DateTime.now().millisecondsSinceEpoch % 10000,
      title: 'AI Suggestion',
      body: suggestion,
      payload: 'ai|suggestion',
      channelId: aiChannelId,
      channelName: 'AI Suggestions',
      channelDescription: 'Smart suggestions from your AI assistant',
    );
  }

  // Core notification scheduling
  Future<void> _scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledDate,
    required String payload,
    required String channelId,
    required String channelName,
    required String channelDescription,
    Priority priority = Priority.defaultPriority,
    bool recurring = false,
  }) async {
    final androidDetails = AndroidNotificationDetails(
      channelId,
      channelName,
      channelDescription: channelDescription,
      importance: _getImportance(priority),
      showWhen: true,
      enableVibration: true,
      playSound: true,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    final notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notifications.zonedSchedule(
      id,
      title,
      body,
      tz.TZDateTime.from(scheduledDate, tz.local),
      notificationDetails,
      payload: payload,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
    );
  }

  Future<void> _showImmediateNotification({
    required int id,
    required String title,
    required String body,
    required String payload,
    required String channelId,
    required String channelName,
    required String channelDescription,
    Priority priority = Priority.defaultPriority,
  }) async {
    final androidDetails = AndroidNotificationDetails(
      channelId,
      channelName,
      channelDescription: channelDescription,
      importance: _getImportance(priority),
    );

    const iosDetails = DarwinNotificationDetails();

    final notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notifications.show(id, title, body, notificationDetails, payload: payload);
  }

  // Cancel notifications
  Future<void> cancelTaskNotifications(int taskId) async {
    final baseId = _generateTaskNotificationId(taskId);
    await _notifications.cancel(baseId);
    await _notifications.cancel(baseId + 1);
    await _notifications.cancel(baseId + 10);
  }

  Future<void> cancelBillNotifications(int billId) async {
    final baseId = _generateBillNotificationId(billId);
    await _notifications.cancel(baseId);
    await _notifications.cancel(baseId + 1);
    await _notifications.cancel(baseId + 2);
  }

  Future<void> cancelAllNotifications() async {
    await _notifications.cancelAll();
  }

  // Utility methods
  int _generateTaskNotificationId(int taskId) => 10000 + taskId;
  int _generateBillNotificationId(int billId) => 20000 + billId;

  Importance _getImportance(Priority priority) {
    switch (priority) {
      case Priority.max:
        return Importance.max;
      case Priority.high:
        return Importance.high;
      case Priority.defaultPriority:
        return Importance.defaultImportance;
      case Priority.low:
        return Importance.low;
      case Priority.min:
        return Importance.min;
    }
  }

  Priority _getPriority(Priority priority) {
    return priority;
  }

  // Get pending notifications
  Future<List<dynamic>> getPendingNotifications() async {
    return await _notifications.pendingNotificationRequests();
  }
}

enum Priority {
  min,
  low,
  defaultPriority,
  high,
  max,
}
