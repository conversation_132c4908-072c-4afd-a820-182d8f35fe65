import 'package:flutter/material.dart';
import '../l10n/generated/app_localizations.dart';

class LocalizationService {
  static AppLocalizations? _localizations;
  
  static AppLocalizations get current {
    if (_localizations == null) {
      throw Exception('Localizations not initialized. Make sure to call LocalizationService.init() first.');
    }
    return _localizations!;
  }
  
  static void init(BuildContext context) {
    _localizations = AppLocalizations.of(context);
  }
  
  static bool get isInitialized => _localizations != null;
  
  // Helper methods for common translations
  static String getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) return current.goodMorning;
    if (hour < 17) return current.goodAfternoon;
    return current.goodEvening;
  }
  
  static String getPriorityText(String priority) {
    switch (priority.toLowerCase()) {
      case 'low':
        return current.low;
      case 'medium':
        return current.medium;
      case 'high':
        return current.high;
      case 'urgent':
        return current.urgent;
      default:
        return priority;
    }
  }
  
  static String getCategoryText(String category) {
    switch (category.toLowerCase()) {
      case 'work':
        return current.work;
      case 'personal':
        return current.personal;
      case 'shopping':
        return current.shopping;
      case 'health':
        return current.health;
      case 'finance':
        return current.finance;
      case 'home':
        return current.home;
      case 'other':
        return current.other;
      default:
        return category;
    }
  }
  
  static String getBillTypeText(String billType) {
    switch (billType.toLowerCase()) {
      case 'electricity':
        return current.electricity;
      case 'water':
        return current.water;
      case 'gas':
        return current.gas;
      case 'internet':
        return current.internet;
      case 'mobile':
        return current.mobile;
      case 'dth':
        return current.dth;
      case 'rent':
        return current.rent;
      case 'insurance':
        return current.insurance;
      case 'loan':
        return current.loan;
      default:
        return billType;
    }
  }
  
  static String getStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return current.pending;
      case 'completed':
        return current.completed;
      case 'overdue':
        return current.overdue;
      case 'paid':
        return current.paid;
      case 'upcoming':
        return current.upcoming;
      default:
        return status;
    }
  }
  
  static String getRecurrenceText(String recurrence) {
    switch (recurrence.toLowerCase()) {
      case 'monthly':
        return current.monthly;
      case 'quarterly':
        return current.quarterly;
      case 'halfyearly':
      case 'half yearly':
        return current.halfYearly;
      case 'yearly':
        return current.yearly;
      default:
        return recurrence;
    }
  }
  
  static String formatDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));
    final yesterday = today.subtract(const Duration(days: 1));
    final dateOnly = DateTime(date.year, date.month, date.day);
    
    if (dateOnly == today) {
      return current.today;
    } else if (dateOnly == tomorrow) {
      return 'Tomorrow'; // Add to localization files if needed
    } else if (dateOnly == yesterday) {
      return 'Yesterday'; // Add to localization files if needed
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
  
  static List<Map<String, String>> getSupportedLanguages() {
    return [
      {
        'code': 'en',
        'name': 'English',
        'nativeName': 'English',
        'flag': '🇺🇸',
      },
      {
        'code': 'hi',
        'name': 'Hindi',
        'nativeName': 'हिंदी',
        'flag': '🇮🇳',
      },
      {
        'code': 'ta',
        'name': 'Tamil',
        'nativeName': 'தமிழ்',
        'flag': '🇮🇳',
      },
      {
        'code': 'te',
        'name': 'Telugu',
        'nativeName': 'తెలుగు',
        'flag': '🇮🇳',
      },
    ];
  }
  
  static String getLanguageName(String languageCode) {
    final languages = getSupportedLanguages();
    final language = languages.firstWhere(
      (lang) => lang['code'] == languageCode,
      orElse: () => languages.first,
    );
    return language['nativeName'] ?? language['name'] ?? 'Unknown';
  }
  
  static String getLanguageFlag(String languageCode) {
    final languages = getSupportedLanguages();
    final language = languages.firstWhere(
      (lang) => lang['code'] == languageCode,
      orElse: () => languages.first,
    );
    return language['flag'] ?? '🌐';
  }
}
