class Bill {
  final int? id;
  final String title;
  final String? description;
  final BillType type;
  final double? amount;
  final DateTime dueDate;
  final DateTime? paidDate;
  final BillStatus status;
  final bool isRecurring;
  final RecurrenceType? recurrenceType;
  final DateTime createdAt;
  final String? notes;

  Bill({
    this.id,
    required this.title,
    this.description,
    required this.type,
    this.amount,
    required this.dueDate,
    this.paidDate,
    this.status = BillStatus.pending,
    this.isRecurring = false,
    this.recurrenceType,
    required this.createdAt,
    this.notes,
  });

  Bill copyWith({
    int? id,
    String? title,
    String? description,
    BillType? type,
    double? amount,
    DateTime? dueDate,
    DateTime? paidDate,
    BillStatus? status,
    bool? isRecurring,
    RecurrenceType? recurrenceType,
    DateTime? createdAt,
    String? notes,
  }) {
    return Bill(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      amount: amount ?? this.amount,
      dueDate: dueDate ?? this.dueDate,
      paidDate: paidDate ?? this.paidDate,
      status: status ?? this.status,
      isRecurring: isRecurring ?? this.isRecurring,
      recurrenceType: recurrenceType ?? this.recurrenceType,
      createdAt: createdAt ?? this.createdAt,
      notes: notes ?? this.notes,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'type': type.index,
      'amount': amount,
      'dueDate': dueDate.millisecondsSinceEpoch,
      'paidDate': paidDate?.millisecondsSinceEpoch,
      'status': status.index,
      'isRecurring': isRecurring ? 1 : 0,
      'recurrenceType': recurrenceType?.index,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'notes': notes,
    };
  }

  factory Bill.fromMap(Map<String, dynamic> map) {
    return Bill(
      id: map['id']?.toInt(),
      title: map['title'] ?? '',
      description: map['description'],
      type: BillType.values[map['type'] ?? 0],
      amount: map['amount']?.toDouble(),
      dueDate: DateTime.fromMillisecondsSinceEpoch(map['dueDate']),
      paidDate: map['paidDate'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(map['paidDate'])
          : null,
      status: BillStatus.values[map['status'] ?? 0],
      isRecurring: (map['isRecurring'] ?? 0) == 1,
      recurrenceType: map['recurrenceType'] != null 
          ? RecurrenceType.values[map['recurrenceType']]
          : null,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      notes: map['notes'],
    );
  }

  bool get isOverdue => DateTime.now().isAfter(dueDate) && status == BillStatus.pending;
  bool get isDueToday => DateTime.now().day == dueDate.day && 
                        DateTime.now().month == dueDate.month && 
                        DateTime.now().year == dueDate.year;
  bool get isDueTomorrow {
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    return tomorrow.day == dueDate.day && 
           tomorrow.month == dueDate.month && 
           tomorrow.year == dueDate.year;
  }

  @override
  String toString() {
    return 'Bill(id: $id, title: $title, type: $type, dueDate: $dueDate, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Bill && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

enum BillType {
  electricity,
  water,
  gas,
  internet,
  mobile,
  dth,
  rent,
  insurance,
  loan,
  other,
}

enum BillStatus {
  pending,
  paid,
  overdue,
  cancelled,
}

enum RecurrenceType {
  monthly,
  quarterly,
  halfYearly,
  yearly,
}

extension BillTypeExtension on BillType {
  String get displayName {
    switch (this) {
      case BillType.electricity:
        return 'Electricity';
      case BillType.water:
        return 'Water';
      case BillType.gas:
        return 'Gas';
      case BillType.internet:
        return 'Internet';
      case BillType.mobile:
        return 'Mobile';
      case BillType.dth:
        return 'DTH';
      case BillType.rent:
        return 'Rent';
      case BillType.insurance:
        return 'Insurance';
      case BillType.loan:
        return 'Loan';
      case BillType.other:
        return 'Other';
    }
  }

  String get icon {
    switch (this) {
      case BillType.electricity:
        return '⚡';
      case BillType.water:
        return '💧';
      case BillType.gas:
        return '🔥';
      case BillType.internet:
        return '🌐';
      case BillType.mobile:
        return '📱';
      case BillType.dth:
        return '📺';
      case BillType.rent:
        return '🏠';
      case BillType.insurance:
        return '🛡️';
      case BillType.loan:
        return '💰';
      case BillType.other:
        return '📄';
    }
  }
}

extension BillStatusExtension on BillStatus {
  String get displayName {
    switch (this) {
      case BillStatus.pending:
        return 'Pending';
      case BillStatus.paid:
        return 'Paid';
      case BillStatus.overdue:
        return 'Overdue';
      case BillStatus.cancelled:
        return 'Cancelled';
    }
  }
}

extension RecurrenceTypeExtension on RecurrenceType {
  String get displayName {
    switch (this) {
      case RecurrenceType.monthly:
        return 'Monthly';
      case RecurrenceType.quarterly:
        return 'Quarterly';
      case RecurrenceType.halfYearly:
        return 'Half Yearly';
      case RecurrenceType.yearly:
        return 'Yearly';
    }
  }

  Duration get duration {
    switch (this) {
      case RecurrenceType.monthly:
        return const Duration(days: 30);
      case RecurrenceType.quarterly:
        return const Duration(days: 90);
      case RecurrenceType.halfYearly:
        return const Duration(days: 180);
      case RecurrenceType.yearly:
        return const Duration(days: 365);
    }
  }
}
