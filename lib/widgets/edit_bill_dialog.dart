import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/bill.dart';
import '../providers/bill_provider.dart';

class EditBillDialog extends StatefulWidget {
  final Bill bill;

  const EditBillDialog({super.key, required this.bill});

  @override
  State<EditBillDialog> createState() => _EditBillDialogState();
}

class _EditBillDialogState extends State<EditBillDialog> {
  late TextEditingController _titleController;
  late TextEditingController _descriptionController;
  late TextEditingController _amountController;
  late TextEditingController _notesController;
  final _formKey = GlobalKey<FormState>();
  
  late BillType _selectedType;
  late DateTime _selectedDueDate;
  late BillStatus _selectedStatus;
  late bool _isRecurring;
  RecurrenceType? _selectedRecurrenceType;

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController(text: widget.bill.title);
    _descriptionController = TextEditingController(text: widget.bill.description ?? '');
    _amountController = TextEditingController(
      text: widget.bill.amount?.toString() ?? ''
    );
    _notesController = TextEditingController(text: widget.bill.notes ?? '');
    
    _selectedType = widget.bill.type;
    _selectedDueDate = widget.bill.dueDate;
    _selectedStatus = widget.bill.status;
    _isRecurring = widget.bill.isRecurring;
    _selectedRecurrenceType = widget.bill.recurrenceType;
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Edit Bill'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'Bill Title',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.receipt),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a bill title';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              
              DropdownButtonFormField<BillType>(
                value: _selectedType,
                decoration: const InputDecoration(
                  labelText: 'Bill Type',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.category),
                ),
                items: BillType.values.map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Row(
                      children: [
                        Text(type.icon, style: const TextStyle(fontSize: 20)),
                        const SizedBox(width: 8),
                        Text(type.displayName),
                      ],
                    ),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedType = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 16),
              
              TextFormField(
                controller: _amountController,
                decoration: const InputDecoration(
                  labelText: 'Amount (₹)',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.currency_rupee),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    final amount = double.tryParse(value);
                    if (amount == null || amount <= 0) {
                      return 'Please enter a valid amount';
                    }
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              
              InkWell(
                onTap: () => _selectDueDate(context),
                child: InputDecorator(
                  decoration: const InputDecoration(
                    labelText: 'Due Date',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.calendar_today),
                  ),
                  child: Text(
                    '${_selectedDueDate.day}/${_selectedDueDate.month}/${_selectedDueDate.year}',
                  ),
                ),
              ),
              const SizedBox(height: 16),
              
              DropdownButtonFormField<BillStatus>(
                value: _selectedStatus,
                decoration: const InputDecoration(
                  labelText: 'Status',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.info),
                ),
                items: BillStatus.values.map((status) {
                  return DropdownMenuItem(
                    value: status,
                    child: Row(
                      children: [
                        Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: _getStatusColor(status),
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(status.displayName),
                      ],
                    ),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedStatus = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 16),
              
              SwitchListTile(
                title: const Text('Recurring Bill'),
                subtitle: const Text('This bill repeats regularly'),
                value: _isRecurring,
                onChanged: (value) {
                  setState(() {
                    _isRecurring = value;
                    if (!value) {
                      _selectedRecurrenceType = null;
                    }
                  });
                },
                contentPadding: EdgeInsets.zero,
              ),
              
              if (_isRecurring) ...[
                const SizedBox(height: 16),
                DropdownButtonFormField<RecurrenceType>(
                  value: _selectedRecurrenceType,
                  decoration: const InputDecoration(
                    labelText: 'Recurrence',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.repeat),
                  ),
                  items: RecurrenceType.values.map((type) {
                    return DropdownMenuItem(
                      value: type,
                      child: Text(type.displayName),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedRecurrenceType = value;
                    });
                  },
                  validator: (value) {
                    if (_isRecurring && value == null) {
                      return 'Please select recurrence type';
                    }
                    return null;
                  },
                ),
              ],
              
              const SizedBox(height: 16),
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description (Optional)',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.description),
                ),
                maxLines: 2,
              ),
              const SizedBox(height: 16),
              
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(
                  labelText: 'Notes (Optional)',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.note),
                ),
                maxLines: 2,
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _saveBill,
          child: const Text('Save Changes'),
        ),
      ],
    );
  }

  Future<void> _selectDueDate(BuildContext context) async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDueDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365 * 2)),
    );
    
    if (date != null) {
      setState(() {
        _selectedDueDate = date;
      });
    }
  }

  void _saveBill() {
    if (_formKey.currentState!.validate()) {
      final amount = _amountController.text.trim().isEmpty 
          ? null 
          : double.parse(_amountController.text.trim());

      final updatedBill = widget.bill.copyWith(
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim().isEmpty 
            ? null 
            : _descriptionController.text.trim(),
        type: _selectedType,
        amount: amount,
        dueDate: _selectedDueDate,
        status: _selectedStatus,
        isRecurring: _isRecurring,
        recurrenceType: _selectedRecurrenceType,
        notes: _notesController.text.trim().isEmpty 
            ? null 
            : _notesController.text.trim(),
        paidDate: _selectedStatus == BillStatus.paid && widget.bill.paidDate == null
            ? DateTime.now()
            : widget.bill.paidDate,
      );

      context.read<BillProvider>().updateBill(updatedBill);
      Navigator.of(context).pop();
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Bill updated successfully!')),
      );
    }
  }

  Color _getStatusColor(BillStatus status) {
    switch (status) {
      case BillStatus.pending:
        return Colors.orange;
      case BillStatus.paid:
        return Colors.green;
      case BillStatus.overdue:
        return Colors.red;
      case BillStatus.cancelled:
        return Colors.grey;
    }
  }
}
