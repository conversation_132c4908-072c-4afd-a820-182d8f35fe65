import 'package:flutter/material.dart';
import '../models/bill.dart';

class BillListWidget extends StatelessWidget {
  final List<Bill> bills;
  final Function(Bill) onBillTap;

  const BillListWidget({
    super.key,
    required this.bills,
    required this.onBillTap,
  });

  @override
  Widget build(BuildContext context) {
    if (bills.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            Icon(
              Icons.receipt_long,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 12),
            Text(
              'No upcoming bills',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'All bills are up to date',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: bills.map((bill) => BillTile(
          bill: bill,
          onTap: () => onBillTap(bill),
        )).toList(),
      ),
    );
  }
}

class BillTile extends StatelessWidget {
  final Bill bill;
  final VoidCallback onTap;

  const BillTile({
    super.key,
    required this.bill,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: Colors.grey.withOpacity(0.1),
              width: 1,
            ),
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: _getStatusColor(bill).withOpacity(0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Center(
                child: Text(
                  bill.type.icon,
                  style: const TextStyle(fontSize: 20),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    bill.title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Due ${_formatDueDate(bill.dueDate)}',
                    style: TextStyle(
                      fontSize: 14,
                      color: _getDueDateColor(bill),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                if (bill.amount != null)
                  Text(
                    '₹${bill.amount!.toStringAsFixed(0)}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                const SizedBox(height: 4),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStatusColor(bill).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    bill.status.displayName,
                    style: TextStyle(
                      fontSize: 12,
                      color: _getStatusColor(bill),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _formatDueDate(DateTime dueDate) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));
    final dateOnly = DateTime(dueDate.year, dueDate.month, dueDate.day);
    
    if (dateOnly == today) {
      return 'today';
    } else if (dateOnly == tomorrow) {
      return 'tomorrow';
    } else {
      final difference = dateOnly.difference(today).inDays;
      if (difference > 0 && difference <= 7) {
        return 'in $difference days';
      } else if (difference < 0) {
        return '${difference.abs()} days ago';
      } else {
        return '${dueDate.day}/${dueDate.month}';
      }
    }
  }

  Color _getDueDateColor(Bill bill) {
    if (bill.isOverdue) {
      return Colors.red;
    } else if (bill.isDueToday) {
      return Colors.orange;
    } else if (bill.isDueTomorrow) {
      return Colors.blue;
    }
    return Colors.grey[600]!;
  }

  Color _getStatusColor(Bill bill) {
    if (bill.isOverdue) {
      return Colors.red;
    }

    switch (bill.status) {
      case BillStatus.pending:
        return Colors.orange;
      case BillStatus.paid:
        return Colors.green;
      case BillStatus.overdue:
        return Colors.red;
      case BillStatus.cancelled:
        return Colors.grey;
    }
  }
}
