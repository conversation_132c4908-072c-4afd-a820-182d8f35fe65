import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/task_provider.dart';

class ProductivityChartWidget extends StatelessWidget {
  const ProductivityChartWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<TaskProvider>(
      builder: (context, taskProvider, child) {
        final stats = taskProvider.getProductivityStats();
        
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.trending_up,
                      color: Colors.green[600],
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'Productivity Analysis',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                
                // Completion Rate
                _buildMetricRow(
                  'Overall Completion Rate',
                  '${stats['completionRate'].toStringAsFixed(1)}%',
                  stats['completionRate'] / 100,
                  _getCompletionRateColor(stats['completionRate']),
                ),
                
                const SizedBox(height: 16),
                
                // Today's Progress
                _buildMetricRow(
                  'Today\'s Progress',
                  '${stats['todayCompleted']}/${stats['todayTotal']}',
                  stats['todayTotal'] == 0 ? 0 : stats['todayCompleted'] / stats['todayTotal'],
                  Colors.blue,
                ),
                
                const SizedBox(height: 16),
                
                // This Week's Progress
                _buildMetricRow(
                  'This Week\'s Progress',
                  '${stats['weekCompleted']}/${stats['weekTotal']}',
                  stats['weekTotal'] == 0 ? 0 : stats['weekCompleted'] / stats['weekTotal'],
                  Colors.purple,
                ),
                
                const SizedBox(height: 16),
                
                // This Month's Progress
                _buildMetricRow(
                  'This Month\'s Progress',
                  '${stats['monthCompleted']}/${stats['monthTotal']}',
                  stats['monthTotal'] == 0 ? 0 : stats['monthCompleted'] / stats['monthTotal'],
                  Colors.orange,
                ),
                
                const SizedBox(height: 20),
                
                // Insights
                _buildInsights(stats),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildMetricRow(String label, String value, double progress, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(color),
          minHeight: 6,
        ),
      ],
    );
  }

  Widget _buildInsights(Map<String, dynamic> stats) {
    final insights = _generateInsights(stats);
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.lightbulb_outline,
                size: 16,
                color: Colors.blue[600],
              ),
              const SizedBox(width: 6),
              Text(
                'AI Insights',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.blue[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ...insights.map((insight) => Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 4,
                  height: 4,
                  margin: const EdgeInsets.only(top: 6, right: 8),
                  decoration: BoxDecoration(
                    color: Colors.blue[600],
                    shape: BoxShape.circle,
                  ),
                ),
                Expanded(
                  child: Text(
                    insight,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.blue[700],
                    ),
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  List<String> _generateInsights(Map<String, dynamic> stats) {
    final insights = <String>[];
    final completionRate = stats['completionRate'];
    final todayRate = stats['todayTotal'] == 0 ? 0 : (stats['todayCompleted'] / stats['todayTotal'] * 100);
    final weekRate = stats['weekTotal'] == 0 ? 0 : (stats['weekCompleted'] / stats['weekTotal'] * 100);
    
    // Completion rate insights
    if (completionRate >= 80) {
      insights.add('Excellent productivity! You\'re completing most of your tasks.');
    } else if (completionRate >= 60) {
      insights.add('Good progress! Consider focusing on high-priority tasks first.');
    } else if (completionRate >= 40) {
      insights.add('Room for improvement. Try breaking large tasks into smaller ones.');
    } else {
      insights.add('Consider reducing your task load or extending deadlines.');
    }
    
    // Today vs week comparison
    if (todayRate > weekRate + 20) {
      insights.add('You\'re having a particularly productive day!');
    } else if (todayRate < weekRate - 20) {
      insights.add('Today seems slower than usual. Take breaks and stay focused.');
    }
    
    // Task volume insights
    if (stats['todayTotal'] > 8) {
      insights.add('Heavy workload today. Prioritize urgent and important tasks.');
    } else if (stats['todayTotal'] < 3) {
      insights.add('Light day ahead. Good time for planning or learning.');
    }
    
    return insights.take(3).toList();
  }

  Color _getCompletionRateColor(double rate) {
    if (rate >= 80) return Colors.green;
    if (rate >= 60) return Colors.blue;
    if (rate >= 40) return Colors.orange;
    return Colors.red;
  }
}
