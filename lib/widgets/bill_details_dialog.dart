import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/bill.dart';
import '../providers/bill_provider.dart';

class BillDetailsDialog extends StatelessWidget {
  final Bill bill;

  const BillDetailsDialog({super.key, required this.bill});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Text(bill.type.icon, style: const TextStyle(fontSize: 24)),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              bill.title,
              style: const TextStyle(fontSize: 18),
            ),
          ),
        ],
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('Type', bill.type.displayName),
            
            if (bill.amount != null)
              _buildDetailRow('Amount', '₹${bill.amount!.toStringAsFixed(2)}'),
            
            _buildDetailRow('Due Date', _formatDate(bill.dueDate)),
            
            _buildDetailRow('Status', bill.status.displayName, 
              color: _getStatusColor(bill.status)),
            
            if (bill.paidDate != null)
              _buildDetailRow('Paid Date', _formatDate(bill.paidDate!)),
            
            if (bill.isRecurring) ...[
              _buildDetailRow('Recurring', 'Yes'),
              if (bill.recurrenceType != null)
                _buildDetailRow('Frequency', bill.recurrenceType!.displayName),
            ] else
              _buildDetailRow('Recurring', 'No'),
            
            _buildDetailRow('Created', _formatDate(bill.createdAt)),
            
            if (bill.description != null && bill.description!.isNotEmpty) ...[
              const SizedBox(height: 16),
              const Text(
                'Description:',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                bill.description!,
                style: const TextStyle(fontSize: 14),
              ),
            ],
            
            if (bill.notes != null && bill.notes!.isNotEmpty) ...[
              const SizedBox(height: 16),
              const Text(
                'Notes:',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                bill.notes!,
                style: const TextStyle(fontSize: 14),
              ),
            ],
            
            // Bill Status Indicators
            const SizedBox(height: 16),
            _buildStatusIndicators(),
          ],
        ),
      ),
      actions: [
        if (bill.status == BillStatus.pending)
          TextButton.icon(
            onPressed: () {
              context.read<BillProvider>().markBillAsPaid(bill);
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('${bill.title} marked as paid')),
              );
            },
            icon: const Icon(Icons.payment),
            label: const Text('Mark as Paid'),
            style: TextButton.styleFrom(foregroundColor: Colors.green),
          ),
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value, {Color? color}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                color: color ?? Colors.black,
                fontWeight: color != null ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusIndicators() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Status Indicators:',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        
        if (bill.isDueToday)
          _buildIndicatorChip(
            'Due Today',
            Icons.today,
            Colors.red,
          ),
        
        if (bill.isDueTomorrow)
          _buildIndicatorChip(
            'Due Tomorrow',
            Icons.schedule,
            Colors.orange,
          ),
        
        if (bill.isOverdue)
          _buildIndicatorChip(
            'Overdue',
            Icons.warning,
            Colors.red,
          ),
        
        if (bill.isRecurring)
          _buildIndicatorChip(
            'Recurring',
            Icons.repeat,
            Colors.blue,
          ),
        
        if (!bill.isDueToday && !bill.isDueTomorrow && !bill.isOverdue && bill.status == BillStatus.pending)
          _buildIndicatorChip(
            'Upcoming',
            Icons.schedule,
            Colors.green,
          ),
      ],
    );
  }

  Widget _buildIndicatorChip(String label, IconData icon, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Chip(
        avatar: Icon(icon, size: 16, color: color),
        label: Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: color,
            fontWeight: FontWeight.w500,
          ),
        ),
        backgroundColor: color.withOpacity(0.1),
        side: BorderSide(color: color.withOpacity(0.3)),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));
    final yesterday = today.subtract(const Duration(days: 1));
    final dateOnly = DateTime(date.year, date.month, date.day);
    
    if (dateOnly == today) {
      return 'Today';
    } else if (dateOnly == tomorrow) {
      return 'Tomorrow';
    } else if (dateOnly == yesterday) {
      return 'Yesterday';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  Color _getStatusColor(BillStatus status) {
    switch (status) {
      case BillStatus.pending:
        return Colors.orange;
      case BillStatus.paid:
        return Colors.green;
      case BillStatus.overdue:
        return Colors.red;
      case BillStatus.cancelled:
        return Colors.grey;
    }
  }
}
