import 'package:flutter/material.dart';
import '../models/task.dart';
import '../models/bill.dart';
import '../services/ai_planning_service.dart';
import '../l10n/generated/app_localizations.dart';
import '../services/localization_service.dart';

class DayPlannerWidget extends StatefulWidget {
  final List<Task> tasks;
  final List<Bill> bills;

  const DayPlannerWidget({
    super.key,
    required this.tasks,
    required this.bills,
  });

  @override
  State<DayPlannerWidget> createState() => _DayPlannerWidgetState();
}

class _DayPlannerWidgetState extends State<DayPlannerWidget> {
  List<DayPlanningSuggestion> _suggestions = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _generateSuggestions();
  }

  @override
  void didUpdateWidget(DayPlannerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.tasks != widget.tasks || oldWidget.bills != widget.bills) {
      _generateSuggestions();
    }
  }

  void _generateSuggestions() {
    setState(() {
      _isLoading = true;
    });

    // Simulate AI processing time
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        final suggestions = AIPlanningService.generateDayPlan(
          tasks: widget.tasks,
          bills: widget.bills,
          targetDate: DateTime.now(),
        );

        setState(() {
          _suggestions = suggestions;
          _isLoading = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.psychology,
                size: 20,
                color: Colors.blue[600],
              ),
              const SizedBox(width: 8),
              Text(
                'AI Assistant',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.blue[600],
                ),
              ),
              const Spacer(),
              if (_isLoading)
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.blue[600]!),
                  ),
                )
              else
                IconButton(
                  icon: const Icon(Icons.refresh, size: 18),
                  onPressed: _generateSuggestions,
                  tooltip: 'Refresh suggestions',
                  constraints: const BoxConstraints(
                    minWidth: 32,
                    minHeight: 32,
                  ),
                ),
            ],
          ),
          const SizedBox(height: 12),

          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(20),
                child: Text(
                  'Analyzing your day...',
                  style: TextStyle(
                    fontSize: 14,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            )
          else if (_suggestions.isEmpty)
            _buildEmptyState(l10n)
          else
            _buildSuggestionsList(),
        ],
      ),
    );
  }

  Widget _buildEmptyState(AppLocalizations l10n) {
    return Column(
      children: [
        Icon(
          Icons.celebration,
          size: 48,
          color: Colors.green[400],
        ),
        const SizedBox(height: 12),
        Text(
          l10n.greatAllCaughtUp,
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          l10n.noUrgentTasksOrBills,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[500],
          ),
        ),
        const SizedBox(height: 16),
        _buildMotivationalTip(),
      ],
    );
  }

  Widget _buildSuggestionsList() {
    final l10n = AppLocalizations.of(context)!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.heresSuggestion,
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey[700],
            fontStyle: FontStyle.italic,
          ),
        ),
        const SizedBox(height: 12),
        ..._suggestions.map((suggestion) => _buildSuggestionCard(suggestion)),
      ],
    );
  }

  Widget _buildSuggestionCard(DayPlanningSuggestion suggestion) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _getSuggestionColor(suggestion.type).withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _getSuggestionColor(suggestion.type).withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: _getSuggestionColor(suggestion.type).withOpacity(0.2),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Center(
              child: Text(
                suggestion.icon,
                style: const TextStyle(fontSize: 16),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        suggestion.title,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    if (suggestion.estimatedTime > 0)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.grey[200],
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Text(
                          '${suggestion.estimatedTime}m',
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.grey[600],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  suggestion.description,
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.grey[600],
                    height: 1.3,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMotivationalTip() {
    final tips = [
      '💡 Consider planning tomorrow\'s priorities',
      '🌱 Take time to learn something new',
      '🎯 Review your completed achievements',
      '☕ Enjoy a well-deserved break',
      '📚 Catch up on reading or personal projects',
    ];

    final randomTip = tips[DateTime.now().millisecond % tips.length];

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.blue[200]!,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.lightbulb_outline,
            size: 20,
            color: Colors.blue[600],
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              randomTip,
              style: TextStyle(
                fontSize: 13,
                color: Colors.blue[700],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getSuggestionColor(SuggestionType type) {
    switch (type) {
      case SuggestionType.urgent:
        return Colors.red;
      case SuggestionType.priority:
        return Colors.orange;
      case SuggestionType.deadline:
        return Colors.amber;
      case SuggestionType.productivity:
        return Colors.blue;
      case SuggestionType.wellness:
        return Colors.green;
      case SuggestionType.planning:
        return Colors.purple;
      case SuggestionType.reminder:
        return Colors.teal;
    }
  }
}
