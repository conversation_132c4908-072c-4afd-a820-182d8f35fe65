import 'package:flutter/material.dart';
import '../models/task.dart';
import '../models/bill.dart';

class DayPlannerWidget extends StatelessWidget {
  final List<Task> tasks;
  final List<Bill> bills;

  const DayPlannerWidget({
    super.key,
    required this.tasks,
    required this.bills,
  });

  @override
  Widget build(BuildContext context) {
    final suggestions = _generateSuggestions();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (suggestions.isEmpty)
            Column(
              children: [
                Icon(
                  Icons.psychology,
                  size: 48,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 12),
                Text(
                  'Great! You\'re all caught up',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'No urgent tasks or bills for today',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[500],
                  ),
                ),
              ],
            )
          else ...[
            Text(
              'Sure, here\'s a suggestion:',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[700],
                fontStyle: FontStyle.italic,
              ),
            ),
            const SizedBox(height: 12),
            ...suggestions.map((suggestion) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 6,
                    height: 6,
                    margin: const EdgeInsets.only(top: 8, right: 12),
                    decoration: const BoxDecoration(
                      color: Colors.blue,
                      shape: BoxShape.circle,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      suggestion,
                      style: const TextStyle(
                        fontSize: 15,
                        height: 1.4,
                      ),
                    ),
                  ),
                ],
              ),
            )),
          ],
        ],
      ),
    );
  }

  List<String> _generateSuggestions() {
    final suggestions = <String>[];
    
    // Get pending tasks for today
    final pendingTasks = tasks.where((task) => !task.isCompleted).toList();
    
    // Get urgent tasks
    final urgentTasks = pendingTasks.where((task) => task.priority == TaskPriority.urgent).toList();
    
    // Get high priority tasks
    final highPriorityTasks = pendingTasks.where((task) => task.priority == TaskPriority.high).toList();
    
    // Get overdue tasks
    final overdueTasks = pendingTasks.where((task) => 
      task.dueDate != null && task.dueDate!.isBefore(DateTime.now())
    ).toList();
    
    // Get bills due today
    final billsDueToday = bills.where((bill) => bill.isDueToday).toList();
    
    // Generate suggestions based on priority
    if (overdueTasks.isNotEmpty) {
      suggestions.add('Complete overdue task: ${overdueTasks.first.title}');
    }
    
    if (urgentTasks.isNotEmpty) {
      suggestions.add('Focus on urgent task: ${urgentTasks.first.title}');
    }
    
    if (billsDueToday.isNotEmpty) {
      suggestions.add('Pay ${billsDueToday.first.title} bill (due today)');
    }
    
    if (highPriorityTasks.isNotEmpty && suggestions.length < 3) {
      suggestions.add('Work on: ${highPriorityTasks.first.title}');
    }
    
    // Add general suggestions if we have pending tasks
    if (pendingTasks.isNotEmpty && suggestions.length < 3) {
      final remainingTasks = pendingTasks.where((task) => 
        !urgentTasks.contains(task) && 
        !highPriorityTasks.contains(task) &&
        !overdueTasks.contains(task)
      ).toList();
      
      if (remainingTasks.isNotEmpty) {
        suggestions.add('Complete: ${remainingTasks.first.title}');
      }
    }
    
    // Add motivational suggestions
    if (suggestions.isEmpty && pendingTasks.isEmpty) {
      suggestions.addAll([
        'Take a break and enjoy your free time',
        'Plan tomorrow\'s tasks',
        'Review your completed goals',
      ]);
    } else if (suggestions.length == 1) {
      suggestions.addAll([
        'Take short breaks between tasks',
        'Stay hydrated throughout the day',
      ]);
    } else if (suggestions.length == 2) {
      suggestions.add('Remember to take breaks between tasks');
    }
    
    return suggestions.take(3).toList();
  }
}
