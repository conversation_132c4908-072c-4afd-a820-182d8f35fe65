import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/task_provider.dart';
import '../providers/bill_provider.dart';
import '../models/task.dart';

class WorkloadAnalysisWidget extends StatelessWidget {
  const WorkloadAnalysisWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer2<TaskProvider, BillProvider>(
      builder: (context, taskProvider, billProvider, child) {
        final workloadData = _analyzeWorkload(taskProvider, billProvider);
        
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.analytics,
                      color: Colors.purple[600],
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'Workload Analysis',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                
                // Workload Intensity
                _buildWorkloadIntensity(workloadData),
                
                const SizedBox(height: 20),
                
                // Priority Distribution
                _buildPriorityDistribution(workloadData),
                
                const SizedBox(height: 20),
                
                // Stress Level Indicator
                _buildStressLevelIndicator(workloadData),
                
                const SizedBox(height: 20),
                
                // Recommendations
                _buildRecommendations(workloadData),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildWorkloadIntensity(WorkloadData data) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Workload Intensity',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _getWorkloadDescription(data.intensity),
                    style: TextStyle(
                      fontSize: 14,
                      color: _getWorkloadColor(data.intensity),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  LinearProgressIndicator(
                    value: data.intensity,
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(
                      _getWorkloadColor(data.intensity),
                    ),
                    minHeight: 8,
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: _getWorkloadColor(data.intensity).withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: _getWorkloadColor(data.intensity).withOpacity(0.3),
                ),
              ),
              child: Text(
                '${(data.intensity * 100).toInt()}%',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: _getWorkloadColor(data.intensity),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPriorityDistribution(WorkloadData data) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Priority Distribution',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        
        ...TaskPriority.values.map((priority) {
          final count = data.priorityDistribution[priority] ?? 0;
          final percentage = data.totalTasks == 0 ? 0.0 : count / data.totalTasks;
          
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              children: [
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: _getPriorityColor(priority),
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 8),
                SizedBox(
                  width: 60,
                  child: Text(
                    priority.displayName,
                    style: const TextStyle(fontSize: 12),
                  ),
                ),
                Expanded(
                  child: LinearProgressIndicator(
                    value: percentage,
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(
                      _getPriorityColor(priority),
                    ),
                    minHeight: 4,
                  ),
                ),
                const SizedBox(width: 8),
                SizedBox(
                  width: 30,
                  child: Text(
                    count.toString(),
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.end,
                  ),
                ),
              ],
            ),
          );
        }),
      ],
    );
  }

  Widget _buildStressLevelIndicator(WorkloadData data) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _getStressColor(data.stressLevel).withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _getStressColor(data.stressLevel).withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            _getStressIcon(data.stressLevel),
            color: _getStressColor(data.stressLevel),
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Stress Level: ${_getStressDescription(data.stressLevel)}',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: _getStressColor(data.stressLevel),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _getStressAdvice(data.stressLevel),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendations(WorkloadData data) {
    final recommendations = _generateRecommendations(data);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'AI Recommendations',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        
        ...recommendations.map((recommendation) => Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: Colors.green[50],
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: Colors.green[200]!),
          ),
          child: Row(
            children: [
              Icon(
                Icons.lightbulb_outline,
                size: 16,
                color: Colors.green[600],
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  recommendation,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.green[700],
                  ),
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }

  WorkloadData _analyzeWorkload(TaskProvider taskProvider, BillProvider billProvider) {
    final pendingTasks = taskProvider.pendingTasks;
    final overdueTasks = taskProvider.getOverdueTasks();
    final overdueBills = billProvider.overdueBills;
    
    // Calculate workload intensity
    double intensity = 0.0;
    for (final task in pendingTasks) {
      switch (task.priority) {
        case TaskPriority.urgent:
          intensity += 0.4;
          break;
        case TaskPriority.high:
          intensity += 0.3;
          break;
        case TaskPriority.medium:
          intensity += 0.2;
          break;
        case TaskPriority.low:
          intensity += 0.1;
          break;
      }
    }
    intensity = (intensity / 3.0).clamp(0.0, 1.0); // Normalize to 0-1
    
    // Calculate stress level
    double stressLevel = 0.0;
    stressLevel += overdueTasks.length * 0.3;
    stressLevel += overdueBills.length * 0.25;
    stressLevel += pendingTasks.where((t) => t.priority == TaskPriority.urgent).length * 0.2;
    stressLevel = stressLevel.clamp(0.0, 1.0);
    
    // Priority distribution
    final priorityDistribution = <TaskPriority, int>{};
    for (final priority in TaskPriority.values) {
      priorityDistribution[priority] = pendingTasks.where((t) => t.priority == priority).length;
    }
    
    return WorkloadData(
      intensity: intensity,
      stressLevel: stressLevel,
      totalTasks: pendingTasks.length,
      priorityDistribution: priorityDistribution,
      overdueTasks: overdueTasks.length,
      overdueBills: overdueBills.length,
    );
  }

  List<String> _generateRecommendations(WorkloadData data) {
    final recommendations = <String>[];
    
    if (data.intensity > 0.8) {
      recommendations.add('Consider postponing non-urgent tasks to reduce workload');
    }
    
    if (data.stressLevel > 0.7) {
      recommendations.add('Take regular breaks to manage stress levels');
    }
    
    if (data.overdueTasks > 0) {
      recommendations.add('Focus on completing overdue tasks first');
    }
    
    final urgentTasks = data.priorityDistribution[TaskPriority.urgent] ?? 0;
    if (urgentTasks > 3) {
      recommendations.add('Too many urgent tasks - review priorities');
    }
    
    if (data.totalTasks > 10) {
      recommendations.add('Break large tasks into smaller, manageable chunks');
    }
    
    if (recommendations.isEmpty) {
      recommendations.add('Good workload balance! Keep up the great work');
    }
    
    return recommendations.take(3).toList();
  }

  String _getWorkloadDescription(double intensity) {
    if (intensity > 0.8) return 'Very High';
    if (intensity > 0.6) return 'High';
    if (intensity > 0.4) return 'Moderate';
    if (intensity > 0.2) return 'Light';
    return 'Very Light';
  }

  Color _getWorkloadColor(double intensity) {
    if (intensity > 0.8) return Colors.red;
    if (intensity > 0.6) return Colors.orange;
    if (intensity > 0.4) return Colors.yellow[700]!;
    if (intensity > 0.2) return Colors.blue;
    return Colors.green;
  }

  String _getStressDescription(double stressLevel) {
    if (stressLevel > 0.7) return 'High';
    if (stressLevel > 0.4) return 'Moderate';
    return 'Low';
  }

  Color _getStressColor(double stressLevel) {
    if (stressLevel > 0.7) return Colors.red;
    if (stressLevel > 0.4) return Colors.orange;
    return Colors.green;
  }

  IconData _getStressIcon(double stressLevel) {
    if (stressLevel > 0.7) return Icons.warning;
    if (stressLevel > 0.4) return Icons.info;
    return Icons.check_circle;
  }

  String _getStressAdvice(double stressLevel) {
    if (stressLevel > 0.7) return 'Consider taking breaks and reducing workload';
    if (stressLevel > 0.4) return 'Monitor your workload and take regular breaks';
    return 'Great job maintaining a healthy work-life balance!';
  }

  Color _getPriorityColor(TaskPriority priority) {
    switch (priority) {
      case TaskPriority.urgent:
        return Colors.red;
      case TaskPriority.high:
        return Colors.orange;
      case TaskPriority.medium:
        return Colors.blue;
      case TaskPriority.low:
        return Colors.green;
    }
  }
}

class WorkloadData {
  final double intensity;
  final double stressLevel;
  final int totalTasks;
  final Map<TaskPriority, int> priorityDistribution;
  final int overdueTasks;
  final int overdueBills;

  WorkloadData({
    required this.intensity,
    required this.stressLevel,
    required this.totalTasks,
    required this.priorityDistribution,
    required this.overdueTasks,
    required this.overdueBills,
  });
}
