import 'package:flutter/material.dart';
import '../models/task.dart';

class TaskFilterWidget extends StatelessWidget {
  final TaskFilterOptions currentFilter;
  final Function(TaskFilterOptions) onFilterChanged;

  const TaskFilterWidget({
    super.key,
    required this.currentFilter,
    required this.onFilterChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text(
            'Filter Tasks',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // Priority Filter
          const Text(
            'Priority',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: [
              FilterChip(
                label: const Text('All'),
                selected: currentFilter.priority == null,
                onSelected: (selected) {
                  if (selected) {
                    onFilterChanged(currentFilter.copyWith(priority: null));
                  }
                },
              ),
              ...TaskPriority.values.map((priority) => FilterChip(
                label: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: _getPriorityColor(priority),
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(priority.displayName),
                  ],
                ),
                selected: currentFilter.priority == priority,
                onSelected: (selected) {
                  onFilterChanged(currentFilter.copyWith(
                    priority: selected ? priority : null,
                  ));
                },
              )),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Category Filter
          const Text(
            'Category',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: [
              FilterChip(
                label: const Text('All'),
                selected: currentFilter.category == null,
                onSelected: (selected) {
                  if (selected) {
                    onFilterChanged(currentFilter.copyWith(category: null));
                  }
                },
              ),
              ..._getCategories().map((category) => FilterChip(
                label: Text(category),
                selected: currentFilter.category == category,
                onSelected: (selected) {
                  onFilterChanged(currentFilter.copyWith(
                    category: selected ? category : null,
                  ));
                },
              )),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Date Filter
          const Text(
            'Due Date',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: DateFilter.values.map((dateFilter) => FilterChip(
              label: Text(dateFilter.displayName),
              selected: currentFilter.dateFilter == dateFilter,
              onSelected: (selected) {
                onFilterChanged(currentFilter.copyWith(
                  dateFilter: selected ? dateFilter : DateFilter.all,
                ));
              },
            )).toList(),
          ),
          
          const SizedBox(height: 16),
          
          // Status Filter
          Row(
            children: [
              Expanded(
                child: CheckboxListTile(
                  title: const Text('Show Completed'),
                  value: currentFilter.showCompleted,
                  onChanged: (value) {
                    onFilterChanged(currentFilter.copyWith(
                      showCompleted: value ?? false,
                    ));
                  },
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Action Buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              TextButton(
                onPressed: () {
                  onFilterChanged(TaskFilterOptions());
                },
                child: const Text('Clear All'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: const Text('Apply'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  List<String> _getCategories() {
    return [
      'Work',
      'Personal',
      'Shopping',
      'Health',
      'Finance',
      'Home',
      'Other',
    ];
  }

  Color _getPriorityColor(TaskPriority priority) {
    switch (priority) {
      case TaskPriority.urgent:
        return Colors.red;
      case TaskPriority.high:
        return Colors.orange;
      case TaskPriority.medium:
        return Colors.blue;
      case TaskPriority.low:
        return Colors.green;
    }
  }
}

class TaskFilterOptions {
  final TaskPriority? priority;
  final String? category;
  final DateFilter dateFilter;
  final bool showCompleted;

  TaskFilterOptions({
    this.priority,
    this.category,
    this.dateFilter = DateFilter.all,
    this.showCompleted = true,
  });

  TaskFilterOptions copyWith({
    TaskPriority? priority,
    String? category,
    DateFilter? dateFilter,
    bool? showCompleted,
  }) {
    return TaskFilterOptions(
      priority: priority,
      category: category,
      dateFilter: dateFilter ?? this.dateFilter,
      showCompleted: showCompleted ?? this.showCompleted,
    );
  }

  bool get hasActiveFilters {
    return priority != null ||
           category != null ||
           dateFilter != DateFilter.all ||
           !showCompleted;
  }
}

enum DateFilter {
  all,
  overdue,
  today,
  tomorrow,
  thisWeek,
  noDueDate,
}

extension DateFilterExtension on DateFilter {
  String get displayName {
    switch (this) {
      case DateFilter.all:
        return 'All';
      case DateFilter.overdue:
        return 'Overdue';
      case DateFilter.today:
        return 'Today';
      case DateFilter.tomorrow:
        return 'Tomorrow';
      case DateFilter.thisWeek:
        return 'This Week';
      case DateFilter.noDueDate:
        return 'No Due Date';
    }
  }
}
