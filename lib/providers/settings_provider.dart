import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SettingsProvider extends ChangeNotifier {
  static const String _languageKey = 'selected_language';
  static const String _themeKey = 'theme_mode';
  static const String _notificationsKey = 'notifications_enabled';
  static const String _taskNotificationsKey = 'task_notifications_enabled';
  static const String _billNotificationsKey = 'bill_notifications_enabled';
  static const String _aiSuggestionsKey = 'ai_suggestions_enabled';
  static const String _dailyTaskSummaryKey = 'daily_task_summary_enabled';
  static const String _dailyPlanningKey = 'daily_planning_enabled';
  static const String _taskReminderMinutesKey = 'task_reminder_minutes';
  static const String _billFirstReminderDaysKey = 'bill_first_reminder_days';
  static const String _billFinalReminderDaysKey = 'bill_final_reminder_days';
  static const String _quietHoursEnabledKey = 'quiet_hours_enabled';
  static const String _quietHoursStartKey = 'quiet_hours_start';
  static const String _quietHoursEndKey = 'quiet_hours_end';

  Locale _currentLocale = const Locale('en', 'US');
  ThemeMode _themeMode = ThemeMode.system;
  bool _notificationsEnabled = true;
  bool _taskNotificationsEnabled = true;
  bool _billNotificationsEnabled = true;
  bool _aiSuggestionsEnabled = true;
  bool _dailyTaskSummaryEnabled = true;
  bool _dailyPlanningEnabled = true;
  int _taskReminderMinutes = 60;
  int _billFirstReminderDays = 3;
  int _billFinalReminderDays = 1;
  bool _quietHoursEnabled = false;
  TimeOfDay _quietHoursStart = const TimeOfDay(hour: 22, minute: 0);
  TimeOfDay _quietHoursEnd = const TimeOfDay(hour: 7, minute: 0);

  Locale get currentLocale => _currentLocale;
  ThemeMode get themeMode => _themeMode;
  bool get notificationsEnabled => _notificationsEnabled;
  bool get taskNotificationsEnabled => _taskNotificationsEnabled;
  bool get billNotificationsEnabled => _billNotificationsEnabled;
  bool get aiSuggestionsEnabled => _aiSuggestionsEnabled;
  bool get dailyTaskSummaryEnabled => _dailyTaskSummaryEnabled;
  bool get dailyPlanningEnabled => _dailyPlanningEnabled;
  int get taskReminderMinutes => _taskReminderMinutes;
  int get billFirstReminderDays => _billFirstReminderDays;
  int get billFinalReminderDays => _billFinalReminderDays;
  bool get quietHoursEnabled => _quietHoursEnabled;
  TimeOfDay get quietHoursStart => _quietHoursStart;
  TimeOfDay get quietHoursEnd => _quietHoursEnd;

  SettingsProvider() {
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();

    // Load language
    final languageCode = prefs.getString(_languageKey) ?? 'en';
    _currentLocale = _getLocaleFromCode(languageCode);

    // Load theme
    final themeIndex = prefs.getInt(_themeKey) ?? 0;
    _themeMode = ThemeMode.values[themeIndex];

    // Load notifications
    _notificationsEnabled = prefs.getBool(_notificationsKey) ?? true;
    _taskNotificationsEnabled = prefs.getBool(_taskNotificationsKey) ?? true;
    _billNotificationsEnabled = prefs.getBool(_billNotificationsKey) ?? true;
    _aiSuggestionsEnabled = prefs.getBool(_aiSuggestionsKey) ?? true;
    _dailyTaskSummaryEnabled = prefs.getBool(_dailyTaskSummaryKey) ?? true;
    _dailyPlanningEnabled = prefs.getBool(_dailyPlanningKey) ?? true;
    _taskReminderMinutes = prefs.getInt(_taskReminderMinutesKey) ?? 60;
    _billFirstReminderDays = prefs.getInt(_billFirstReminderDaysKey) ?? 3;
    _billFinalReminderDays = prefs.getInt(_billFinalReminderDaysKey) ?? 1;
    _quietHoursEnabled = prefs.getBool(_quietHoursEnabledKey) ?? false;

    // Load quiet hours times
    final startHour = prefs.getInt('${_quietHoursStartKey}_hour') ?? 22;
    final startMinute = prefs.getInt('${_quietHoursStartKey}_minute') ?? 0;
    _quietHoursStart = TimeOfDay(hour: startHour, minute: startMinute);

    final endHour = prefs.getInt('${_quietHoursEndKey}_hour') ?? 7;
    final endMinute = prefs.getInt('${_quietHoursEndKey}_minute') ?? 0;
    _quietHoursEnd = TimeOfDay(hour: endHour, minute: endMinute);

    notifyListeners();
  }

  Future<void> setLanguage(String languageCode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_languageKey, languageCode);
    
    _currentLocale = _getLocaleFromCode(languageCode);
    notifyListeners();
  }

  Future<void> setThemeMode(ThemeMode themeMode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_themeKey, themeMode.index);
    
    _themeMode = themeMode;
    notifyListeners();
  }

  Future<void> setNotificationsEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_notificationsKey, enabled);

    _notificationsEnabled = enabled;
    notifyListeners();
  }

  Future<void> setTaskNotificationsEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_taskNotificationsKey, enabled);

    _taskNotificationsEnabled = enabled;
    notifyListeners();
  }

  Future<void> setBillNotificationsEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_billNotificationsKey, enabled);

    _billNotificationsEnabled = enabled;
    notifyListeners();
  }

  Future<void> setAISuggestionsEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_aiSuggestionsKey, enabled);

    _aiSuggestionsEnabled = enabled;
    notifyListeners();
  }

  Future<void> setDailyTaskSummaryEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_dailyTaskSummaryKey, enabled);

    _dailyTaskSummaryEnabled = enabled;
    notifyListeners();
  }

  Future<void> setDailyPlanningEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_dailyPlanningKey, enabled);

    _dailyPlanningEnabled = enabled;
    notifyListeners();
  }

  Future<void> setTaskReminderMinutes(int minutes) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_taskReminderMinutesKey, minutes);

    _taskReminderMinutes = minutes;
    notifyListeners();
  }

  Future<void> setBillFirstReminderDays(int days) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_billFirstReminderDaysKey, days);

    _billFirstReminderDays = days;
    notifyListeners();
  }

  Future<void> setBillFinalReminderDays(int days) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_billFinalReminderDaysKey, days);

    _billFinalReminderDays = days;
    notifyListeners();
  }

  Future<void> setQuietHoursEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_quietHoursEnabledKey, enabled);

    _quietHoursEnabled = enabled;
    notifyListeners();
  }

  Future<void> setQuietHoursStart(TimeOfDay time) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('${_quietHoursStartKey}_hour', time.hour);
    await prefs.setInt('${_quietHoursStartKey}_minute', time.minute);

    _quietHoursStart = time;
    notifyListeners();
  }

  Future<void> setQuietHoursEnd(TimeOfDay time) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('${_quietHoursEndKey}_hour', time.hour);
    await prefs.setInt('${_quietHoursEndKey}_minute', time.minute);

    _quietHoursEnd = time;
    notifyListeners();
  }

  Locale _getLocaleFromCode(String code) {
    switch (code) {
      case 'hi':
        return const Locale('hi', 'IN');
      case 'ta':
        return const Locale('ta', 'IN');
      case 'te':
        return const Locale('te', 'IN');
      default:
        return const Locale('en', 'US');
    }
  }

  String get currentLanguageCode {
    return _currentLocale.languageCode;
  }

  String get currentLanguageName {
    switch (_currentLocale.languageCode) {
      case 'hi':
        return 'हिंदी';
      case 'ta':
        return 'தமிழ்';
      case 'te':
        return 'తెలుగు';
      default:
        return 'English';
    }
  }

  List<Map<String, String>> get supportedLanguages => [
    {'code': 'en', 'name': 'English', 'nativeName': 'English'},
    {'code': 'hi', 'name': 'Hindi', 'nativeName': 'हिंदी'},
    {'code': 'ta', 'name': 'Tamil', 'nativeName': 'தமிழ்'},
    {'code': 'te', 'name': 'Telugu', 'nativeName': 'తెలుగు'},
  ];
}
