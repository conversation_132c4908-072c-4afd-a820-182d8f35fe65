import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SettingsProvider extends ChangeNotifier {
  static const String _languageKey = 'selected_language';
  static const String _themeKey = 'theme_mode';
  static const String _notificationsKey = 'notifications_enabled';

  Locale _currentLocale = const Locale('en', 'US');
  ThemeMode _themeMode = ThemeMode.system;
  bool _notificationsEnabled = true;

  Locale get currentLocale => _currentLocale;
  ThemeMode get themeMode => _themeMode;
  bool get notificationsEnabled => _notificationsEnabled;

  SettingsProvider() {
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    
    // Load language
    final languageCode = prefs.getString(_languageKey) ?? 'en';
    _currentLocale = _getLocaleFromCode(languageCode);
    
    // Load theme
    final themeIndex = prefs.getInt(_themeKey) ?? 0;
    _themeMode = ThemeMode.values[themeIndex];
    
    // Load notifications
    _notificationsEnabled = prefs.getBool(_notificationsKey) ?? true;
    
    notifyListeners();
  }

  Future<void> setLanguage(String languageCode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_languageKey, languageCode);
    
    _currentLocale = _getLocaleFromCode(languageCode);
    notifyListeners();
  }

  Future<void> setThemeMode(ThemeMode themeMode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_themeKey, themeMode.index);
    
    _themeMode = themeMode;
    notifyListeners();
  }

  Future<void> setNotificationsEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_notificationsKey, enabled);
    
    _notificationsEnabled = enabled;
    notifyListeners();
  }

  Locale _getLocaleFromCode(String code) {
    switch (code) {
      case 'hi':
        return const Locale('hi', 'IN');
      case 'ta':
        return const Locale('ta', 'IN');
      case 'te':
        return const Locale('te', 'IN');
      default:
        return const Locale('en', 'US');
    }
  }

  String get currentLanguageCode {
    return _currentLocale.languageCode;
  }

  String get currentLanguageName {
    switch (_currentLocale.languageCode) {
      case 'hi':
        return 'हिंदी';
      case 'ta':
        return 'தமிழ்';
      case 'te':
        return 'తెలుగు';
      default:
        return 'English';
    }
  }

  List<Map<String, String>> get supportedLanguages => [
    {'code': 'en', 'name': 'English', 'nativeName': 'English'},
    {'code': 'hi', 'name': 'Hindi', 'nativeName': 'हिंदी'},
    {'code': 'ta', 'name': 'Tamil', 'nativeName': 'தமிழ்'},
    {'code': 'te', 'name': 'Telugu', 'nativeName': 'తెలుగు'},
  ];
}
