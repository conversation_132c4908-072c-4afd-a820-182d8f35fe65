import 'package:flutter/material.dart';
import '../models/task.dart';
import '../services/database_service.dart';
import '../services/notification_service.dart';

class TaskProvider extends ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService.instance;
  
  List<Task> _tasks = [];
  List<Task> _todayTasks = [];
  bool _isLoading = false;

  List<Task> get tasks => _tasks;
  List<Task> get todayTasks => _todayTasks;
  List<Task> get completedTasks => _tasks.where((task) => task.isCompleted).toList();
  List<Task> get pendingTasks => _tasks.where((task) => !task.isCompleted).toList();
  bool get isLoading => _isLoading;

  TaskProvider() {
    loadTasks();
  }

  Future<void> loadTasks() async {
    _isLoading = true;
    notifyListeners();

    try {
      _tasks = await _databaseService.getAllTasks();
      _todayTasks = await _databaseService.getTasksDueToday();
    } catch (e) {
      debugPrint('Error loading tasks: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> addTask(Task task) async {
    try {
      final id = await _databaseService.insertTask(task);
      final newTask = task.copyWith(id: id);
      _tasks.insert(0, newTask);

      // Update today's tasks if the new task is due today
      if (_isTaskDueToday(newTask)) {
        _todayTasks.add(newTask);
        _sortTodayTasks();
      }

      // Schedule notification if task has due date
      if (newTask.dueDate != null) {
        await NotificationService.instance.scheduleTaskReminder(newTask);
        await NotificationService.instance.scheduleTaskDeadlineAlert(newTask);
      }

      notifyListeners();
    } catch (e) {
      debugPrint('Error adding task: $e');
    }
  }

  Future<void> updateTask(Task task) async {
    try {
      await _databaseService.updateTask(task);
      
      final index = _tasks.indexWhere((t) => t.id == task.id);
      if (index != -1) {
        _tasks[index] = task;
      }
      
      // Update today's tasks
      final todayIndex = _todayTasks.indexWhere((t) => t.id == task.id);
      if (_isTaskDueToday(task)) {
        if (todayIndex != -1) {
          _todayTasks[todayIndex] = task;
        } else {
          _todayTasks.add(task);
        }
        _sortTodayTasks();
      } else if (todayIndex != -1) {
        _todayTasks.removeAt(todayIndex);
      }
      
      notifyListeners();
    } catch (e) {
      debugPrint('Error updating task: $e');
    }
  }

  Future<void> deleteTask(int taskId) async {
    try {
      await _databaseService.deleteTask(taskId);
      _tasks.removeWhere((task) => task.id == taskId);
      _todayTasks.removeWhere((task) => task.id == taskId);

      // Cancel notifications for this task
      await NotificationService.instance.cancelTaskNotifications(taskId);

      notifyListeners();
    } catch (e) {
      debugPrint('Error deleting task: $e');
    }
  }

  Future<void> toggleTaskCompletion(Task task) async {
    final updatedTask = task.copyWith(isCompleted: !task.isCompleted);
    await updateTask(updatedTask);
  }

  List<Task> getTasksByPriority(TaskPriority priority) {
    return _tasks.where((task) => task.priority == priority && !task.isCompleted).toList();
  }

  List<Task> getTasksByCategory(String category) {
    return _tasks.where((task) => task.category == category && !task.isCompleted).toList();
  }

  List<Task> getOverdueTasks() {
    final now = DateTime.now();
    return _tasks.where((task) => 
      !task.isCompleted && 
      task.dueDate != null && 
      task.dueDate!.isBefore(now)
    ).toList();
  }

  List<Task> getUpcomingTasks({int days = 7}) {
    final now = DateTime.now();
    final futureDate = now.add(Duration(days: days));
    
    return _tasks.where((task) => 
      !task.isCompleted && 
      task.dueDate != null && 
      task.dueDate!.isAfter(now) &&
      task.dueDate!.isBefore(futureDate)
    ).toList()..sort((a, b) => a.dueDate!.compareTo(b.dueDate!));
  }

  bool _isTaskDueToday(Task task) {
    if (task.dueDate == null) return false;
    
    final now = DateTime.now();
    final dueDate = task.dueDate!;
    
    return now.year == dueDate.year &&
           now.month == dueDate.month &&
           now.day == dueDate.day;
  }

  void _sortTodayTasks() {
    _todayTasks.sort((a, b) {
      // First sort by completion status (incomplete first)
      if (a.isCompleted != b.isCompleted) {
        return a.isCompleted ? 1 : -1;
      }
      
      // Then sort by priority
      final priorityComparison = a.priority.sortOrder.compareTo(b.priority.sortOrder);
      if (priorityComparison != 0) {
        return priorityComparison;
      }
      
      // Finally sort by due time if available
      if (a.dueDate != null && b.dueDate != null) {
        return a.dueDate!.compareTo(b.dueDate!);
      }
      
      return 0;
    });
  }

  // Get task statistics
  Map<String, int> getTaskStatistics() {
    final total = _tasks.length;
    final completed = completedTasks.length;
    final pending = pendingTasks.length;
    final overdue = getOverdueTasks().length;
    final dueToday = _todayTasks.where((task) => !task.isCompleted).length;
    
    return {
      'total': total,
      'completed': completed,
      'pending': pending,
      'overdue': overdue,
      'dueToday': dueToday,
    };
  }

  // Search tasks
  List<Task> searchTasks(String query) {
    if (query.isEmpty) return _tasks;

    final lowercaseQuery = query.toLowerCase();
    return _tasks.where((task) =>
      task.title.toLowerCase().contains(lowercaseQuery) ||
      (task.description?.toLowerCase().contains(lowercaseQuery) ?? false) ||
      (task.category?.toLowerCase().contains(lowercaseQuery) ?? false)
    ).toList();
  }

  // Bulk operations
  Future<void> markMultipleTasksComplete(List<int> taskIds) async {
    for (final taskId in taskIds) {
      final task = _tasks.firstWhere((t) => t.id == taskId);
      await updateTask(task.copyWith(isCompleted: true));
    }
  }

  Future<void> deleteMultipleTasks(List<int> taskIds) async {
    for (final taskId in taskIds) {
      await deleteTask(taskId);
    }
  }

  // Get tasks by date range
  List<Task> getTasksByDateRange(DateTime start, DateTime end) {
    return _tasks.where((task) =>
      task.dueDate != null &&
      task.dueDate!.isAfter(start.subtract(const Duration(days: 1))) &&
      task.dueDate!.isBefore(end.add(const Duration(days: 1)))
    ).toList()..sort((a, b) => a.dueDate!.compareTo(b.dueDate!));
  }

  // Get tasks for this week
  List<Task> getTasksThisWeek() {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final endOfWeek = startOfWeek.add(const Duration(days: 6));

    return getTasksByDateRange(startOfWeek, endOfWeek);
  }

  // Get productivity statistics
  Map<String, dynamic> getProductivityStats() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final thisWeek = today.subtract(Duration(days: now.weekday - 1));
    final thisMonth = DateTime(now.year, now.month, 1);

    final todayTasks = _tasks.where((task) =>
      task.createdAt.isAfter(today) ||
      (task.dueDate != null && _isToday(task.dueDate!))
    ).toList();

    final thisWeekTasks = _tasks.where((task) =>
      task.createdAt.isAfter(thisWeek)
    ).toList();

    final thisMonthTasks = _tasks.where((task) =>
      task.createdAt.isAfter(thisMonth)
    ).toList();

    return {
      'todayCompleted': todayTasks.where((t) => t.isCompleted).length,
      'todayTotal': todayTasks.length,
      'weekCompleted': thisWeekTasks.where((t) => t.isCompleted).length,
      'weekTotal': thisWeekTasks.length,
      'monthCompleted': thisMonthTasks.where((t) => t.isCompleted).length,
      'monthTotal': thisMonthTasks.length,
      'completionRate': _tasks.isEmpty ? 0.0 :
        completedTasks.length / _tasks.length * 100,
    };
  }

  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
           date.month == now.month &&
           date.day == now.day;
  }
}
