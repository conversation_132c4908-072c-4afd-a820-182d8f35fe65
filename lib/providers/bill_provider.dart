import 'package:flutter/material.dart';
import '../models/bill.dart';
import '../services/database_service.dart';

class Bill<PERSON><PERSON>ider extends ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService.instance;
  
  List<Bill> _bills = [];
  List<Bill> _upcomingBills = [];
  bool _isLoading = false;

  List<Bill> get bills => _bills;
  List<Bill> get upcomingBills => _upcomingBills;
  List<Bill> get paidBills => _bills.where((bill) => bill.status == BillStatus.paid).toList();
  List<Bill> get pendingBills => _bills.where((bill) => bill.status == BillStatus.pending).toList();
  List<Bill> get overdueBills => _bills.where((bill) => bill.isOverdue).toList();
  bool get isLoading => _isLoading;

  BillProvider() {
    loadBills();
  }

  Future<void> loadBills() async {
    _isLoading = true;
    notifyListeners();

    try {
      _bills = await _databaseService.getAllBills();
      _upcomingBills = await _databaseService.getUpcomingBills();
    } catch (e) {
      debugPrint('Error loading bills: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> addBill(Bill bill) async {
    try {
      final id = await _databaseService.insertBill(bill);
      final newBill = bill.copyWith(id: id);
      _bills.add(newBill);
      
      // Update upcoming bills if this bill is upcoming
      if (newBill.status == BillStatus.pending && 
          newBill.dueDate.isAfter(DateTime.now())) {
        _upcomingBills.add(newBill);
        _sortUpcomingBills();
      }
      
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding bill: $e');
    }
  }

  Future<void> updateBill(Bill bill) async {
    try {
      await _databaseService.updateBill(bill);
      
      final index = _bills.indexWhere((b) => b.id == bill.id);
      if (index != -1) {
        _bills[index] = bill;
      }
      
      // Update upcoming bills
      final upcomingIndex = _upcomingBills.indexWhere((b) => b.id == bill.id);
      if (bill.status == BillStatus.pending && bill.dueDate.isAfter(DateTime.now())) {
        if (upcomingIndex != -1) {
          _upcomingBills[upcomingIndex] = bill;
        } else {
          _upcomingBills.add(bill);
        }
        _sortUpcomingBills();
      } else if (upcomingIndex != -1) {
        _upcomingBills.removeAt(upcomingIndex);
      }
      
      notifyListeners();
    } catch (e) {
      debugPrint('Error updating bill: $e');
    }
  }

  Future<void> deleteBill(int billId) async {
    try {
      await _databaseService.deleteBill(billId);
      _bills.removeWhere((bill) => bill.id == billId);
      _upcomingBills.removeWhere((bill) => bill.id == billId);
      notifyListeners();
    } catch (e) {
      debugPrint('Error deleting bill: $e');
    }
  }

  Future<void> markBillAsPaid(Bill bill) async {
    final updatedBill = bill.copyWith(
      status: BillStatus.paid,
      paidDate: DateTime.now(),
    );
    await updateBill(updatedBill);
    
    // If it's a recurring bill, create the next occurrence
    if (bill.isRecurring && bill.recurrenceType != null) {
      await _createNextRecurringBill(updatedBill);
    }
  }

  Future<void> _createNextRecurringBill(Bill paidBill) async {
    if (paidBill.recurrenceType == null) return;
    
    final nextDueDate = _calculateNextDueDate(paidBill.dueDate, paidBill.recurrenceType!);
    
    final nextBill = Bill(
      title: paidBill.title,
      description: paidBill.description,
      type: paidBill.type,
      amount: paidBill.amount,
      dueDate: nextDueDate,
      status: BillStatus.pending,
      isRecurring: true,
      recurrenceType: paidBill.recurrenceType,
      createdAt: DateTime.now(),
      notes: paidBill.notes,
    );
    
    await addBill(nextBill);
  }

  DateTime _calculateNextDueDate(DateTime currentDueDate, RecurrenceType recurrenceType) {
    switch (recurrenceType) {
      case RecurrenceType.monthly:
        return DateTime(
          currentDueDate.year,
          currentDueDate.month + 1,
          currentDueDate.day,
        );
      case RecurrenceType.quarterly:
        return DateTime(
          currentDueDate.year,
          currentDueDate.month + 3,
          currentDueDate.day,
        );
      case RecurrenceType.halfYearly:
        return DateTime(
          currentDueDate.year,
          currentDueDate.month + 6,
          currentDueDate.day,
        );
      case RecurrenceType.yearly:
        return DateTime(
          currentDueDate.year + 1,
          currentDueDate.month,
          currentDueDate.day,
        );
    }
  }

  void _sortUpcomingBills() {
    _upcomingBills.sort((a, b) => a.dueDate.compareTo(b.dueDate));
  }

  List<Bill> getBillsByType(BillType type) {
    return _bills.where((bill) => bill.type == type).toList();
  }

  List<Bill> getBillsByStatus(BillStatus status) {
    return _bills.where((bill) => bill.status == status).toList();
  }

  List<Bill> getBillsDueToday() {
    return _bills.where((bill) => bill.isDueToday && bill.status == BillStatus.pending).toList();
  }

  List<Bill> getBillsDueTomorrow() {
    return _bills.where((bill) => bill.isDueTomorrow && bill.status == BillStatus.pending).toList();
  }

  List<Bill> getBillsDueThisWeek() {
    final now = DateTime.now();
    final endOfWeek = now.add(Duration(days: 7 - now.weekday));
    
    return _bills.where((bill) => 
      bill.status == BillStatus.pending &&
      bill.dueDate.isAfter(now) &&
      bill.dueDate.isBefore(endOfWeek)
    ).toList()..sort((a, b) => a.dueDate.compareTo(b.dueDate));
  }

  // Get bill statistics
  Map<String, dynamic> getBillStatistics() {
    final total = _bills.length;
    final paid = paidBills.length;
    final pending = pendingBills.length;
    final overdue = overdueBills.length;
    final dueToday = getBillsDueToday().length;
    final dueTomorrow = getBillsDueTomorrow().length;
    
    final totalAmount = _bills.fold<double>(0, (sum, bill) => sum + (bill.amount ?? 0));
    final paidAmount = paidBills.fold<double>(0, (sum, bill) => sum + (bill.amount ?? 0));
    final pendingAmount = pendingBills.fold<double>(0, (sum, bill) => sum + (bill.amount ?? 0));
    
    return {
      'total': total,
      'paid': paid,
      'pending': pending,
      'overdue': overdue,
      'dueToday': dueToday,
      'dueTomorrow': dueTomorrow,
      'totalAmount': totalAmount,
      'paidAmount': paidAmount,
      'pendingAmount': pendingAmount,
    };
  }

  // Search bills
  List<Bill> searchBills(String query) {
    if (query.isEmpty) return _bills;

    final lowercaseQuery = query.toLowerCase();
    return _bills.where((bill) =>
      bill.title.toLowerCase().contains(lowercaseQuery) ||
      (bill.description?.toLowerCase().contains(lowercaseQuery) ?? false) ||
      bill.type.displayName.toLowerCase().contains(lowercaseQuery)
    ).toList();
  }

  // Bulk operations
  Future<void> markMultipleBillsAsPaid(List<int> billIds) async {
    for (final billId in billIds) {
      final bill = _bills.firstWhere((b) => b.id == billId);
      await markBillAsPaid(bill);
    }
  }

  Future<void> deleteMultipleBills(List<int> billIds) async {
    for (final billId in billIds) {
      await deleteBill(billId);
    }
  }

  // Get bills by date range
  List<Bill> getBillsByDateRange(DateTime start, DateTime end) {
    return _bills.where((bill) =>
      bill.dueDate.isAfter(start.subtract(const Duration(days: 1))) &&
      bill.dueDate.isBefore(end.add(const Duration(days: 1)))
    ).toList()..sort((a, b) => a.dueDate.compareTo(b.dueDate));
  }

  // Get bills for this month
  List<Bill> getBillsThisMonth() {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);

    return getBillsByDateRange(startOfMonth, endOfMonth);
  }

  // Get recurring bills
  List<Bill> getRecurringBills() {
    return _bills.where((bill) => bill.isRecurring).toList();
  }

  // Get bills by amount range
  List<Bill> getBillsByAmountRange(double minAmount, double maxAmount) {
    return _bills.where((bill) =>
      bill.amount != null &&
      bill.amount! >= minAmount &&
      bill.amount! <= maxAmount
    ).toList();
  }

  // Get payment history
  List<Bill> getPaymentHistory({int? months}) {
    final paidBills = _bills.where((bill) => bill.status == BillStatus.paid).toList();

    if (months != null) {
      final cutoffDate = DateTime.now().subtract(Duration(days: months * 30));
      return paidBills.where((bill) =>
        bill.paidDate != null && bill.paidDate!.isAfter(cutoffDate)
      ).toList()..sort((a, b) => b.paidDate!.compareTo(a.paidDate!));
    }

    return paidBills..sort((a, b) => b.paidDate!.compareTo(a.paidDate!));
  }

  // Get spending analytics
  Map<String, dynamic> getSpendingAnalytics() {
    final now = DateTime.now();
    final thisMonth = DateTime(now.year, now.month, 1);
    final lastMonth = DateTime(now.year, now.month - 1, 1);
    final thisYear = DateTime(now.year, 1, 1);

    final thisMonthBills = _bills.where((bill) =>
      bill.paidDate != null && bill.paidDate!.isAfter(thisMonth)
    ).toList();

    final lastMonthBills = _bills.where((bill) =>
      bill.paidDate != null &&
      bill.paidDate!.isAfter(lastMonth) &&
      bill.paidDate!.isBefore(thisMonth)
    ).toList();

    final thisYearBills = _bills.where((bill) =>
      bill.paidDate != null && bill.paidDate!.isAfter(thisYear)
    ).toList();

    final thisMonthSpent = thisMonthBills.fold<double>(0, (sum, bill) => sum + (bill.amount ?? 0));
    final lastMonthSpent = lastMonthBills.fold<double>(0, (sum, bill) => sum + (bill.amount ?? 0));
    final thisYearSpent = thisYearBills.fold<double>(0, (sum, bill) => sum + (bill.amount ?? 0));

    // Category-wise spending
    final categorySpending = <BillType, double>{};
    for (final bill in thisMonthBills) {
      categorySpending[bill.type] = (categorySpending[bill.type] ?? 0) + (bill.amount ?? 0);
    }

    return {
      'thisMonthSpent': thisMonthSpent,
      'lastMonthSpent': lastMonthSpent,
      'thisYearSpent': thisYearSpent,
      'monthlyChange': lastMonthSpent == 0 ? 0 : ((thisMonthSpent - lastMonthSpent) / lastMonthSpent * 100),
      'categorySpending': categorySpending,
      'averageMonthlySpending': thisYearSpent / now.month,
    };
  }

  // Get upcoming payment reminders
  List<Bill> getUpcomingReminders({int days = 7}) {
    final now = DateTime.now();
    final futureDate = now.add(Duration(days: days));

    return _bills.where((bill) =>
      bill.status == BillStatus.pending &&
      bill.dueDate.isAfter(now) &&
      bill.dueDate.isBefore(futureDate)
    ).toList()..sort((a, b) => a.dueDate.compareTo(b.dueDate));
  }

  // Check for duplicate bills
  List<Bill> findPotentialDuplicates(Bill newBill) {
    return _bills.where((bill) =>
      bill.title.toLowerCase() == newBill.title.toLowerCase() &&
      bill.type == newBill.type &&
      bill.dueDate.month == newBill.dueDate.month &&
      bill.id != newBill.id
    ).toList();
  }
}
