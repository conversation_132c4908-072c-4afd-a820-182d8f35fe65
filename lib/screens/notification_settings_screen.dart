import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/settings_provider.dart';
import '../services/notification_service.dart';
import '../l10n/generated/app_localizations.dart';

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  State<NotificationSettingsScreen> createState() => _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState extends State<NotificationSettingsScreen> {
  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Settings'),
      ),
      body: Consumer<SettingsProvider>(
        builder: (context, settings, child) {
          return ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // Master notification toggle
              Card(
                child: SwitchListTile(
                  title: const Text('Enable Notifications'),
                  subtitle: const Text('Turn on/off all notifications'),
                  value: settings.notificationsEnabled,
                  onChanged: (value) {
                    settings.setNotificationsEnabled(value);
                    if (!value) {
                      NotificationService.instance.cancelAllNotifications();
                    }
                  },
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Task notifications section
              _buildSectionHeader('Task Notifications'),
              Card(
                child: Column(
                  children: [
                    SwitchListTile(
                      title: const Text('Task Reminders'),
                      subtitle: const Text('Get notified before task due times'),
                      value: settings.taskNotificationsEnabled,
                      onChanged: settings.notificationsEnabled 
                          ? (value) => settings.setTaskNotificationsEnabled(value)
                          : null,
                    ),
                    const Divider(height: 1),
                    ListTile(
                      title: const Text('Reminder Time'),
                      subtitle: Text('${settings.taskReminderMinutes} minutes before due time'),
                      trailing: const Icon(Icons.chevron_right),
                      enabled: settings.notificationsEnabled && settings.taskNotificationsEnabled,
                      onTap: () => _showReminderTimeDialog(
                        context,
                        'Task Reminder Time',
                        settings.taskReminderMinutes,
                        (value) => settings.setTaskReminderMinutes(value),
                      ),
                    ),
                    const Divider(height: 1),
                    SwitchListTile(
                      title: const Text('Daily Task Summary'),
                      subtitle: const Text('Morning summary of today\'s tasks'),
                      value: settings.dailyTaskSummaryEnabled,
                      onChanged: settings.notificationsEnabled 
                          ? (value) => settings.setDailyTaskSummaryEnabled(value)
                          : null,
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Bill notifications section
              _buildSectionHeader('Bill Notifications'),
              Card(
                child: Column(
                  children: [
                    SwitchListTile(
                      title: const Text('Bill Reminders'),
                      subtitle: const Text('Get notified before bill due dates'),
                      value: settings.billNotificationsEnabled,
                      onChanged: settings.notificationsEnabled 
                          ? (value) => settings.setBillNotificationsEnabled(value)
                          : null,
                    ),
                    const Divider(height: 1),
                    ListTile(
                      title: const Text('First Reminder'),
                      subtitle: Text('${settings.billFirstReminderDays} days before due date'),
                      trailing: const Icon(Icons.chevron_right),
                      enabled: settings.notificationsEnabled && settings.billNotificationsEnabled,
                      onTap: () => _showReminderDaysDialog(
                        context,
                        'First Bill Reminder',
                        settings.billFirstReminderDays,
                        (value) => settings.setBillFirstReminderDays(value),
                      ),
                    ),
                    const Divider(height: 1),
                    ListTile(
                      title: const Text('Final Reminder'),
                      subtitle: Text('${settings.billFinalReminderDays} days before due date'),
                      trailing: const Icon(Icons.chevron_right),
                      enabled: settings.notificationsEnabled && settings.billNotificationsEnabled,
                      onTap: () => _showReminderDaysDialog(
                        context,
                        'Final Bill Reminder',
                        settings.billFinalReminderDays,
                        (value) => settings.setBillFinalReminderDays(value),
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 16),
              
              // AI suggestions section
              _buildSectionHeader('AI Suggestions'),
              Card(
                child: Column(
                  children: [
                    SwitchListTile(
                      title: const Text('AI Suggestions'),
                      subtitle: const Text('Smart productivity suggestions'),
                      value: settings.aiSuggestionsEnabled,
                      onChanged: settings.notificationsEnabled 
                          ? (value) => settings.setAISuggestionsEnabled(value)
                          : null,
                    ),
                    const Divider(height: 1),
                    SwitchListTile(
                      title: const Text('Daily Planning'),
                      subtitle: const Text('Morning planning reminders'),
                      value: settings.dailyPlanningEnabled,
                      onChanged: settings.notificationsEnabled 
                          ? (value) => settings.setDailyPlanningEnabled(value)
                          : null,
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Quiet hours section
              _buildSectionHeader('Quiet Hours'),
              Card(
                child: Column(
                  children: [
                    SwitchListTile(
                      title: const Text('Enable Quiet Hours'),
                      subtitle: const Text('Silence notifications during specific hours'),
                      value: settings.quietHoursEnabled,
                      onChanged: settings.notificationsEnabled 
                          ? (value) => settings.setQuietHoursEnabled(value)
                          : null,
                    ),
                    if (settings.quietHoursEnabled) ...[
                      const Divider(height: 1),
                      ListTile(
                        title: const Text('Start Time'),
                        subtitle: Text(_formatTime(settings.quietHoursStart)),
                        trailing: const Icon(Icons.chevron_right),
                        onTap: () => _showTimePicker(
                          context,
                          'Quiet Hours Start',
                          settings.quietHoursStart,
                          (time) => settings.setQuietHoursStart(time),
                        ),
                      ),
                      const Divider(height: 1),
                      ListTile(
                        title: const Text('End Time'),
                        subtitle: Text(_formatTime(settings.quietHoursEnd)),
                        trailing: const Icon(Icons.chevron_right),
                        onTap: () => _showTimePicker(
                          context,
                          'Quiet Hours End',
                          settings.quietHoursEnd,
                          (time) => settings.setQuietHoursEnd(time),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Test notification button
              ElevatedButton.icon(
                onPressed: settings.notificationsEnabled ? _sendTestNotification : null,
                icon: const Icon(Icons.notifications_active),
                label: const Text('Send Test Notification'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Pending notifications info
              FutureBuilder<List<dynamic>>(
                future: NotificationService.instance.getPendingNotifications(),
                builder: (context, snapshot) {
                  if (snapshot.hasData) {
                    final count = snapshot.data!.length;
                    return Card(
                      child: ListTile(
                        leading: const Icon(Icons.schedule),
                        title: const Text('Pending Notifications'),
                        subtitle: Text('$count notifications scheduled'),
                        trailing: TextButton(
                          onPressed: count > 0 ? _showPendingNotifications : null,
                          child: const Text('View'),
                        ),
                      ),
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Colors.grey[700],
        ),
      ),
    );
  }

  void _showReminderTimeDialog(
    BuildContext context,
    String title,
    int currentValue,
    Function(int) onChanged,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [15, 30, 60, 120, 240].map((minutes) {
            return RadioListTile<int>(
              title: Text('$minutes minutes'),
              value: minutes,
              groupValue: currentValue,
              onChanged: (value) {
                if (value != null) {
                  onChanged(value);
                  Navigator.of(context).pop();
                }
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  void _showReminderDaysDialog(
    BuildContext context,
    String title,
    int currentValue,
    Function(int) onChanged,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [1, 2, 3, 5, 7].map((days) {
            return RadioListTile<int>(
              title: Text('$days ${days == 1 ? 'day' : 'days'}'),
              value: days,
              groupValue: currentValue,
              onChanged: (value) {
                if (value != null) {
                  onChanged(value);
                  Navigator.of(context).pop();
                }
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  void _showTimePicker(
    BuildContext context,
    String title,
    TimeOfDay currentTime,
    Function(TimeOfDay) onChanged,
  ) async {
    final time = await showTimePicker(
      context: context,
      initialTime: currentTime,
    );
    
    if (time != null) {
      onChanged(time);
    }
  }

  String _formatTime(TimeOfDay time) {
    final hour = time.hourOfPeriod;
    final minute = time.minute.toString().padLeft(2, '0');
    final period = time.period == DayPeriod.am ? 'AM' : 'PM';
    return '${hour == 0 ? 12 : hour}:$minute $period';
  }

  void _sendTestNotification() {
    NotificationService.instance.scheduleAISuggestionNotification(
      'This is a test notification from LifeLoop! 🎉',
    );
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Test notification sent!')),
    );
  }

  void _showPendingNotifications() {
    // TODO: Implement pending notifications viewer
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Pending notifications viewer coming soon!')),
    );
  }
}
