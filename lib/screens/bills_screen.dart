import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/bill_provider.dart';
import '../models/bill.dart';
import '../widgets/bill_list_widget.dart';
import '../widgets/add_bill_button.dart';
import '../widgets/bill_statistics_widget.dart';
import '../widgets/edit_bill_dialog.dart';
import '../widgets/bill_details_dialog.dart';

class BillsScreen extends StatefulWidget {
  const BillsScreen({super.key});

  @override
  State<BillsScreen> createState() => _BillsScreenState();
}

class _BillsScreenState extends State<BillsScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  String _searchQuery = '';
  BillFilter _currentFilter = BillFilter.all;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Bills & Payments'),
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(text: 'All'),
            Tab(text: 'Upcoming'),
            Tab(text: 'Due Today'),
            Tab(text: 'Overdue'),
            Tab(text: 'Paid'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'statistics':
                  _showStatisticsDialog();
                  break;
                case 'export':
                  _exportBills();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'statistics',
                child: Row(
                  children: [
                    Icon(Icons.analytics),
                    SizedBox(width: 8),
                    Text('Statistics'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'export',
                child: Row(
                  children: [
                    Icon(Icons.download),
                    SizedBox(width: 8),
                    Text('Export'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          if (_searchQuery.isNotEmpty || _currentFilter != BillFilter.all)
            Container(
              padding: const EdgeInsets.all(16),
              color: Colors.blue.withOpacity(0.1),
              child: Row(
                children: [
                  if (_searchQuery.isNotEmpty) ...[
                    Chip(
                      label: Text('Search: $_searchQuery'),
                      onDeleted: () {
                        setState(() {
                          _searchQuery = '';
                        });
                      },
                    ),
                    const SizedBox(width: 8),
                  ],
                  if (_currentFilter != BillFilter.all) ...[
                    Chip(
                      label: Text('Filter: ${_currentFilter.displayName}'),
                      onDeleted: () {
                        setState(() {
                          _currentFilter = BillFilter.all;
                        });
                      },
                    ),
                  ],
                  const Spacer(),
                  TextButton(
                    onPressed: () {
                      setState(() {
                        _searchQuery = '';
                        _currentFilter = BillFilter.all;
                      });
                    },
                    child: const Text('Clear All'),
                  ),
                ],
              ),
            ),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildBillList(BillListType.all),
                _buildBillList(BillListType.upcoming),
                _buildBillList(BillListType.dueToday),
                _buildBillList(BillListType.overdue),
                _buildBillList(BillListType.paid),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: const AddBillButton(),
    );
  }

  Widget _buildBillList(BillListType type) {
    return Consumer<BillProvider>(
      builder: (context, billProvider, child) {
        if (billProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        List<Bill> bills = _getFilteredBills(billProvider, type);
        
        if (bills.isEmpty) {
          return _buildEmptyState(type);
        }

        return RefreshIndicator(
          onRefresh: () => billProvider.loadBills(),
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: bills.length,
            itemBuilder: (context, index) {
              final bill = bills[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 8),
                child: BillTile(
                  bill: bill,
                  onTap: () => _showBillDetails(bill),
                  onEdit: () => _editBill(bill),
                  onDelete: () => _deleteBill(bill),
                  onMarkPaid: () => _markBillAsPaid(bill),
                  showActions: true,
                ),
              );
            },
          ),
        );
      },
    );
  }

  List<Bill> _getFilteredBills(BillProvider billProvider, BillListType type) {
    List<Bill> bills;
    
    switch (type) {
      case BillListType.all:
        bills = billProvider.bills;
        break;
      case BillListType.upcoming:
        bills = billProvider.upcomingBills;
        break;
      case BillListType.dueToday:
        bills = billProvider.getBillsDueToday();
        break;
      case BillListType.overdue:
        bills = billProvider.overdueBills;
        break;
      case BillListType.paid:
        bills = billProvider.paidBills;
        break;
    }

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      bills = billProvider.searchBills(_searchQuery);
      // Filter by type after search
      switch (type) {
        case BillListType.upcoming:
          bills = bills.where((bill) => 
            bill.status == BillStatus.pending && 
            bill.dueDate.isAfter(DateTime.now())
          ).toList();
          break;
        case BillListType.dueToday:
          bills = bills.where((bill) => bill.isDueToday).toList();
          break;
        case BillListType.overdue:
          bills = bills.where((bill) => bill.isOverdue).toList();
          break;
        case BillListType.paid:
          bills = bills.where((bill) => bill.status == BillStatus.paid).toList();
          break;
        default:
          break;
      }
    }

    // Apply additional filters
    switch (_currentFilter) {
      case BillFilter.all:
        break;
      case BillFilter.highAmount:
        bills = bills.where((bill) => 
          bill.amount != null && bill.amount! > 1000
        ).toList();
        break;
      case BillFilter.recurring:
        bills = bills.where((bill) => bill.isRecurring).toList();
        break;
      case BillFilter.thisWeek:
        final now = DateTime.now();
        final endOfWeek = now.add(Duration(days: 7 - now.weekday));
        bills = bills.where((bill) => 
          bill.dueDate.isBefore(endOfWeek) && 
          bill.dueDate.isAfter(now.subtract(const Duration(days: 1)))
        ).toList();
        break;
    }

    return bills;
  }

  Widget _buildEmptyState(BillListType type) {
    String message;
    IconData icon;
    
    switch (type) {
      case BillListType.all:
        message = 'No bills yet';
        icon = Icons.receipt_long;
        break;
      case BillListType.upcoming:
        message = 'No upcoming bills';
        icon = Icons.schedule;
        break;
      case BillListType.dueToday:
        message = 'No bills due today';
        icon = Icons.today;
        break;
      case BillListType.overdue:
        message = 'No overdue bills';
        icon = Icons.warning;
        break;
      case BillListType.paid:
        message = 'No paid bills';
        icon = Icons.check_circle;
        break;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Tap + to add a new bill',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Bills'),
        content: TextField(
          autofocus: true,
          decoration: const InputDecoration(
            hintText: 'Enter search term...',
            border: OutlineInputBorder(),
          ),
          onSubmitted: (value) {
            setState(() {
              _searchQuery = value.trim();
            });
            Navigator.of(context).pop();
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Bills'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: BillFilter.values.map((filter) {
            return RadioListTile<BillFilter>(
              title: Text(filter.displayName),
              value: filter,
              groupValue: _currentFilter,
              onChanged: (value) {
                setState(() {
                  _currentFilter = value!;
                });
                Navigator.of(context).pop();
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showStatisticsDialog() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Bill Statistics',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              const BillStatisticsWidget(),
            ],
          ),
        ),
      ),
    );
  }

  void _exportBills() {
    // TODO: Implement bill export functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Export functionality coming soon!')),
    );
  }

  void _showBillDetails(Bill bill) {
    showDialog(
      context: context,
      builder: (context) => BillDetailsDialog(bill: bill),
    );
  }

  void _editBill(Bill bill) {
    showDialog(
      context: context,
      builder: (context) => EditBillDialog(bill: bill),
    );
  }

  void _deleteBill(Bill bill) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Bill'),
        content: Text('Are you sure you want to delete "${bill.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              context.read<BillProvider>().deleteBill(bill.id!);
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Bill deleted')),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _markBillAsPaid(Bill bill) {
    context.read<BillProvider>().markBillAsPaid(bill);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('${bill.title} marked as paid')),
    );
  }
}

enum BillListType { all, upcoming, dueToday, overdue, paid }

enum BillFilter { all, highAmount, recurring, thisWeek }

extension BillFilterExtension on BillFilter {
  String get displayName {
    switch (this) {
      case BillFilter.all:
        return 'All Bills';
      case BillFilter.highAmount:
        return 'High Amount (>₹1000)';
      case BillFilter.recurring:
        return 'Recurring Bills';
      case BillFilter.thisWeek:
        return 'Due This Week';
    }
  }
}
