import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/task_provider.dart';
import '../providers/bill_provider.dart';
import '../services/ai_planning_service.dart';
import '../l10n/generated/app_localizations.dart';
import '../widgets/productivity_chart_widget.dart';
import '../widgets/workload_analysis_widget.dart';

class AIInsightsScreen extends StatefulWidget {
  const AIInsightsScreen({super.key});

  @override
  State<AIInsightsScreen> createState() => _AIInsightsScreenState();
}

class _AIInsightsScreenState extends State<AIInsightsScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  List<DayPlanningSuggestion> _suggestions = [];
  SituationAnalysis? _analysis;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _generateInsights();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _generateInsights() {
    setState(() {
      _isLoading = true;
    });

    final taskProvider = context.read<TaskProvider>();
    final billProvider = context.read<BillProvider>();

    // Simulate AI processing
    Future.delayed(const Duration(milliseconds: 1000), () {
      if (mounted) {
        final suggestions = AIPlanningService.generateDayPlan(
          tasks: taskProvider.tasks,
          bills: billProvider.bills,
          targetDate: DateTime.now(),
          completionHistory: taskProvider.completedTasks,
        );

        setState(() {
          _suggestions = suggestions;
          _isLoading = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Icon(
              Icons.psychology,
              color: Colors.blue[600],
            ),
            const SizedBox(width: 8),
            const Text('AI Insights'),
          ],
        ),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Today\'s Plan'),
            Tab(text: 'Analytics'),
            Tab(text: 'Trends'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _generateInsights,
            tooltip: 'Refresh insights',
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildTodaysPlan(),
          _buildAnalytics(),
          _buildTrends(),
        ],
      ),
    );
  }

  Widget _buildTodaysPlan() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text(
              'AI is analyzing your day...',
              style: TextStyle(
                fontSize: 16,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDayOverview(),
          const SizedBox(height: 24),
          _buildSuggestionsSection(),
          const SizedBox(height: 24),
          _buildTimelineSection(),
        ],
      ),
    );
  }

  Widget _buildDayOverview() {
    final taskProvider = context.watch<TaskProvider>();
    final billProvider = context.watch<BillProvider>();
    
    final stats = taskProvider.getTaskStatistics();
    final billStats = billProvider.getBillStatistics();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.today,
                  color: Colors.blue[600],
                ),
                const SizedBox(width: 8),
                const Text(
                  'Today\'s Overview',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: _buildOverviewCard(
                    'Tasks Due',
                    stats['dueToday'].toString(),
                    Icons.task_alt,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildOverviewCard(
                    'Bills Due',
                    billStats['dueToday'].toString(),
                    Icons.receipt,
                    Colors.orange,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            Row(
              children: [
                Expanded(
                  child: _buildOverviewCard(
                    'Overdue',
                    ((stats['overdue'] ?? 0) + (billStats['overdue'] ?? 0)).toString(),
                    Icons.warning,
                    Colors.red,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildOverviewCard(
                    'Completed',
                    stats['completed'].toString(),
                    Icons.check_circle,
                    Colors.green,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOverviewCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSuggestionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.lightbulb,
                  color: Colors.amber[600],
                ),
                const SizedBox(width: 8),
                const Text(
                  'AI Suggestions',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            if (_suggestions.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(20),
                  child: Text(
                    'No suggestions available',
                    style: TextStyle(
                      fontSize: 14,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ),
              )
            else
              ..._suggestions.map((suggestion) => _buildSuggestionTile(suggestion)),
          ],
        ),
      ),
    );
  }

  Widget _buildSuggestionTile(DayPlanningSuggestion suggestion) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _getSuggestionColor(suggestion.type).withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _getSuggestionColor(suggestion.type).withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: _getSuggestionColor(suggestion.type).withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Center(
              child: Text(
                suggestion.icon,
                style: const TextStyle(fontSize: 20),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        suggestion.title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: _getSuggestionColor(suggestion.type),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${suggestion.estimatedTime}m',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  suggestion.description,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimelineSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.schedule,
                  color: Colors.green[600],
                ),
                const SizedBox(width: 8),
                const Text(
                  'Suggested Timeline',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildTimelineItems(),
          ],
        ),
      ),
    );
  }

  Widget _buildTimelineItems() {
    final now = DateTime.now();
    final timeSlots = _generateTimeSlots(now);

    return Column(
      children: timeSlots.map((slot) => _buildTimeSlot(slot)).toList(),
    );
  }

  Widget _buildTimeSlot(TimeSlot slot) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          SizedBox(
            width: 60,
            child: Text(
              slot.time,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: slot.color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(6),
                border: Border.all(
                  color: slot.color.withOpacity(0.3),
                ),
              ),
              child: Text(
                slot.activity,
                style: const TextStyle(fontSize: 14),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnalytics() {
    return const SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          ProductivityChartWidget(),
          SizedBox(height: 24),
          WorkloadAnalysisWidget(),
        ],
      ),
    );
  }

  Widget _buildTrends() {
    return const Center(
      child: Text(
        'Trends analysis coming soon!',
        style: TextStyle(
          fontSize: 16,
          fontStyle: FontStyle.italic,
        ),
      ),
    );
  }

  Color _getSuggestionColor(SuggestionType type) {
    switch (type) {
      case SuggestionType.urgent:
        return Colors.red;
      case SuggestionType.priority:
        return Colors.orange;
      case SuggestionType.deadline:
        return Colors.amber;
      case SuggestionType.productivity:
        return Colors.blue;
      case SuggestionType.wellness:
        return Colors.green;
      case SuggestionType.planning:
        return Colors.purple;
      case SuggestionType.reminder:
        return Colors.teal;
    }
  }

  List<TimeSlot> _generateTimeSlots(DateTime now) {
    final slots = <TimeSlot>[];
    final hour = now.hour;

    if (hour < 9) {
      slots.add(TimeSlot('9:00 AM', 'Morning focus session', Colors.blue));
    }
    if (hour < 11) {
      slots.add(TimeSlot('11:00 AM', 'Handle urgent tasks', Colors.orange));
    }
    if (hour < 13) {
      slots.add(TimeSlot('1:00 PM', 'Lunch break', Colors.green));
    }
    if (hour < 15) {
      slots.add(TimeSlot('3:00 PM', 'Batch similar tasks', Colors.purple));
    }
    if (hour < 17) {
      slots.add(TimeSlot('5:00 PM', 'Review and plan tomorrow', Colors.teal));
    }

    return slots;
  }
}

class TimeSlot {
  final String time;
  final String activity;
  final Color color;

  TimeSlot(this.time, this.activity, this.color);
}
