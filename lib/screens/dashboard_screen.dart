import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/task_provider.dart';
import '../providers/bill_provider.dart';
import '../providers/settings_provider.dart';
import '../widgets/task_list_widget.dart';
import '../widgets/bill_list_widget.dart';
import '../widgets/day_planner_widget.dart';
import '../widgets/add_task_button.dart';
import '../models/task.dart';
import '../models/bill.dart';
import '../services/localization_service.dart';
import '../l10n/generated/app_localizations.dart';
import 'tasks_screen.dart';
import 'bills_screen.dart';
import 'ai_insights_screen.dart';
import 'notification_settings_screen.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  @override
  void initState() {
    super.initState();
    // Load data when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      LocalizationService.init(context);
      context.read<TaskProvider>().loadTasks();
      context.read<BillProvider>().loadBills();
    });
  }

  @override
  Widget build(BuildContext context) {
    // Initialize localization service
    if (!LocalizationService.isInitialized) {
      LocalizationService.init(context);
    }

    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Row(
          children: [
            Text(
              LocalizationService.getGreeting(),
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            IconButton(
              icon: const Icon(Icons.psychology),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const AIInsightsScreen()),
                );
              },
              tooltip: 'AI Insights',
            ),
          ],
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'language':
                  _showLanguageDialog();
                  break;
                case 'notifications':
                  Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) => const NotificationSettingsScreen()),
                  );
                  break;
                case 'settings':
                  _showSettingsDialog();
                  break;
              }
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'language',
                child: Row(
                  children: [
                    const Icon(Icons.language),
                    const SizedBox(width: 8),
                    Text(l10n.language),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'notifications',
                child: Row(
                  children: [
                    const Icon(Icons.notifications),
                    const SizedBox(width: 8),
                    const Text('Notifications'),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    const Icon(Icons.settings),
                    const SizedBox(width: 8),
                    Text(l10n.settings),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _refreshData,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Today's Tasks Section
              _buildSectionHeader(l10n.todaysTasks, Icons.today, onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const TasksScreen()),
                );
              }),
              const SizedBox(height: 12),
              Consumer<TaskProvider>(
                builder: (context, taskProvider, child) {
                  if (taskProvider.isLoading) {
                    return const Center(child: CircularProgressIndicator());
                  }
                  
                  return TaskListWidget(
                    tasks: taskProvider.todayTasks,
                    onTaskToggle: (task) => taskProvider.toggleTaskCompletion(task),
                    showAddButton: true,
                  );
                },
              ),
              
              const SizedBox(height: 24),
              
              // Upcoming Bills Section
              _buildSectionHeader(l10n.upcomingBills, Icons.receipt_long, onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const BillsScreen()),
                );
              }),
              const SizedBox(height: 12),
              Consumer<BillProvider>(
                builder: (context, billProvider, child) {
                  if (billProvider.isLoading) {
                    return const Center(child: CircularProgressIndicator());
                  }
                  
                  return BillListWidget(
                    bills: billProvider.upcomingBills.take(3).toList(),
                    onBillTap: (bill) => _showBillDetails(bill),
                  );
                },
              ),
              
              const SizedBox(height: 24),
              
              // Plan My Day Section
              _buildSectionHeader(l10n.planMyDay, Icons.schedule),
              const SizedBox(height: 12),
              Consumer2<TaskProvider, BillProvider>(
                builder: (context, taskProvider, billProvider, child) {
                  return DayPlannerWidget(
                    tasks: taskProvider.todayTasks,
                    bills: billProvider.getBillsDueToday(),
                  );
                },
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: const AddTaskButton(),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon, {VoidCallback? onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 8),
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          if (onTap != null) ...[
            const Spacer(),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Colors.grey[400],
            ),
          ],
        ],
      ),
    );
  }



  Future<void> _refreshData() async {
    await Future.wait([
      context.read<TaskProvider>().loadTasks(),
      context.read<BillProvider>().loadBills(),
    ]);
  }

  void _showBillDetails(Bill bill) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(bill.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Type: ${bill.type.displayName}'),
            if (bill.amount != null) Text('Amount: ₹${bill.amount!.toStringAsFixed(2)}'),
            Text('Due: ${_formatDate(bill.dueDate)}'),
            Text('Status: ${bill.status.displayName}'),
            if (bill.description != null) ...[
              const SizedBox(height: 8),
              Text(bill.description!),
            ],
          ],
        ),
        actions: [
          if (bill.status == BillStatus.pending)
            TextButton(
              onPressed: () {
                context.read<BillProvider>().markBillAsPaid(bill);
                Navigator.of(context).pop();
              },
              child: const Text('Mark as Paid'),
            ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showLanguageDialog() {
    final settingsProvider = context.read<SettingsProvider>();
    final l10n = AppLocalizations.of(context)!;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.selectLanguage),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: LocalizationService.getSupportedLanguages().map((lang) {
            return RadioListTile<String>(
              title: Row(
                children: [
                  Text(lang['flag']!, style: const TextStyle(fontSize: 20)),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text('${lang['name']} (${lang['nativeName']})'),
                  ),
                ],
              ),
              value: lang['code']!,
              groupValue: settingsProvider.currentLanguageCode,
              onChanged: (value) {
                if (value != null) {
                  settingsProvider.setLanguage(value);
                  Navigator.of(context).pop();
                }
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n.cancel),
          ),
        ],
      ),
    );
  }

  void _showSettingsDialog() {
    final l10n = AppLocalizations.of(context)!;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.settings),
        content: const Text('Settings screen coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n.close),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));
    final dateOnly = DateTime(date.year, date.month, date.day);
    
    if (dateOnly == today) {
      return 'Today';
    } else if (dateOnly == tomorrow) {
      return 'Tomorrow';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
